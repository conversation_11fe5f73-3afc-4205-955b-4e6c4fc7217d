package org.ruoyi.system.domain.bo;

import java.util.LinkedHashMap;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.ruoyi.system.domain.Noval;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-12T10:45:13+0800",
    comments = "version: 1.5.3.Final, compiler: javac, environment: Java 17.0.12 (Oracle Corporation)"
)
@Component
public class NovalBoToNovalMapperImpl implements NovalBoToNovalMapper {

    @Override
    public Noval convert(NovalBo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        Noval noval = new Noval();

        noval.setSearchValue( arg0.getSearchValue() );
        noval.setCreateDept( arg0.getCreateDept() );
        noval.setCreateBy( arg0.getCreateBy() );
        noval.setCreateTime( arg0.getCreateTime() );
        noval.setUpdateBy( arg0.getUpdateBy() );
        noval.setUpdateTime( arg0.getUpdateTime() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            noval.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        noval.setId( arg0.getId() );
        noval.setName( arg0.getName() );
        noval.setFlag( arg0.getFlag() );
        noval.setAuthor( arg0.getAuthor() );

        return noval;
    }

    @Override
    public Noval convert(NovalBo arg0, Noval arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setSearchValue( arg0.getSearchValue() );
        arg1.setCreateDept( arg0.getCreateDept() );
        arg1.setCreateBy( arg0.getCreateBy() );
        arg1.setCreateTime( arg0.getCreateTime() );
        arg1.setUpdateBy( arg0.getUpdateBy() );
        arg1.setUpdateTime( arg0.getUpdateTime() );
        if ( arg1.getParams() != null ) {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.getParams().clear();
                arg1.getParams().putAll( map );
            }
            else {
                arg1.setParams( null );
            }
        }
        else {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.setParams( new LinkedHashMap<String, Object>( map ) );
            }
        }
        arg1.setId( arg0.getId() );
        arg1.setName( arg0.getName() );
        arg1.setFlag( arg0.getFlag() );
        arg1.setAuthor( arg0.getAuthor() );

        return arg1;
    }
}
