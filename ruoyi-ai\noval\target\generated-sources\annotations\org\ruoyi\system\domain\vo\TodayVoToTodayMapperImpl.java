package org.ruoyi.system.domain.vo;

import javax.annotation.processing.Generated;
import org.ruoyi.system.domain.Today;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-12T10:45:12+0800",
    comments = "version: 1.5.3.Final, compiler: javac, environment: Java 17.0.12 (Oracle Corporation)"
)
@Component
public class TodayVoToTodayMapperImpl implements TodayVoToTodayMapper {

    @Override
    public Today convert(TodayVo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        Today today = new Today();

        today.setId( arg0.getId() );
        today.setNewCommernt( arg0.getNewCommernt() );

        return today;
    }

    @Override
    public Today convert(TodayVo arg0, Today arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setNewCommernt( arg0.getNewCommernt() );

        return arg1;
    }
}
