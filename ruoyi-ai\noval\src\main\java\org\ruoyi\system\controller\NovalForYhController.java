package org.ruoyi.system.controller;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import co.elastic.clients.elasticsearch.ElasticsearchClient;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.ruoyi.common.core.domain.R;
import org.ruoyi.common.log.annotation.Log;
import org.ruoyi.common.log.enums.BusinessType;
import org.ruoyi.system.service.INovalForYhServiceByES;
import org.ruoyi.system.util.Es.ElasticsearchIndexUtil;
import org.ruoyi.system.util.Es.EsIndex;
import java.util.HashMap;
import java.util.Map;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import org.ruoyi.common.web.core.BaseController;
import org.ruoyi.core.page.PageQuery;
import org.ruoyi.system.domain.vo.NovalForYhVo;
import org.ruoyi.system.domain.bo.NovalForYhBo;
import org.ruoyi.core.page.TableDataInfo;

/**
 * 用户浏览书库
 *
 * <AUTHOR>
 * @date 2025-06-11
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/noval/NovalForYh")
public class NovalForYhController extends BaseController {

    private final INovalForYhServiceByES novalForYhService;
    /**
     * 查询用户浏览书库列表
     */
    @GetMapping("/list")
    public TableDataInfo<NovalForYhVo> list(NovalForYhBo bo, PageQuery pageQuery) {
        TableDataInfo<NovalForYhVo> novalForYhVoTableDataInfo = novalForYhService.queryPageList(bo, pageQuery);
        return novalForYhVoTableDataInfo;
    }

    /**
     * 获取用户浏览书库详细信息
     *
     * @param id 主键
     */
    @GetMapping("/{id}")
    public R<NovalForYhVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        System.out.println("Controller接收到getInfo请求，ID: " + id);
        try {
            NovalForYhVo result = novalForYhService.queryById(id);
            if (result != null) {
                System.out.println("Controller返回成功，书名: " + result.getName());
                return R.ok(result);
            } else {
                System.err.println("Controller查询结果为空，ID: " + id);
                return R.fail("未找到指定的小说信息");
            }
        } catch (Exception e) {
            System.err.println("Controller查询异常: " + e.getMessage());
            return R.fail("查询小说信息失败: " + e.getMessage());
        }
    }

    @Log(title = "我的小说")
    @GetMapping("/getInfo/{ids}")
    public R<List<NovalForYhVo>> getList(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        List<NovalForYhVo> novalForYhVos = novalForYhService.queryByUserId(ids);
        System.out.println("-----------------------------");
        System.out.println("novalForYhVos: "+novalForYhVos);
        System.out.println("-----------------------------");
        return R.ok(novalForYhVos);
    }


    /**
     * 自动补全建议接口（增强版）
     * 支持书名、作者、类型等多字段智能补全
     *
     * @param keyword 用户输入的关键词
     * @return 自动补全建议列表
     */
    @GetMapping("/suggest")
    public R<List<String>> suggestNovalNames(@RequestParam String keyword) {
        try {
            System.out.println("=== 自动补全请求开始 ===");
            System.out.println("接收到关键词: " + keyword);

            if (keyword == null || keyword.trim().isEmpty()) {
                System.out.println("关键词为空，返回空列表");
                return R.ok(new ArrayList<>());
            }

            List<String> suggestions = novalForYhService.autoSuggest(keyword.trim());
            System.out.println("自动补全结果数量: " + (suggestions != null ? suggestions.size() : 0));
            System.out.println("自动补全结果: " + suggestions);
            System.out.println("=== 自动补全请求结束 ===");

            return R.ok(suggestions);

        } catch (Exception e) {
            System.err.println("自动补全接口异常: " + e.getMessage());
            e.printStackTrace();
            return R.ok(new ArrayList<>());  // 即使出错也返回空列表，不影响用户体验
        }
    }

    /**
     * 检查ES索引状态和自动补全配置
     */
    @GetMapping("/checkIndex")
    public R<Map<String, Object>> checkIndexStatus() {
        Map<String, Object> result = new HashMap<>();
        var client = org.ruoyi.system.util.Es.ElasticsearchIndexUtil.createClient();

        try {
            // 检查索引是否存在
            boolean indexExists = org.ruoyi.system.util.Es.ElasticsearchIndexUtil.indexExists(client, EsIndex.NovalForYh);
            result.put("indexExists", indexExists);

            if (indexExists) {
                // 检查索引中的文档数量
                try {
                    // 简单查询获取文档数量
                    var searchResponse = client.search(s -> s
                            .index(EsIndex.NovalForYh)
                            .size(0), Object.class);
                    result.put("documentCount", searchResponse.hits().total().value());
                } catch (Exception e) {
                    result.put("documentCount", "查询失败: " + e.getMessage());
                }

                // 测试自动补全字段
                try {
                    List<String> testSuggestions = novalForYhService.autoSuggest("测试");
                    result.put("testSuggestions", testSuggestions);
                    result.put("suggestionsCount", testSuggestions.size());
                } catch (Exception e) {
                    result.put("testSuggestions", "测试失败: " + e.getMessage());
                }
            }

            result.put("status", "success");
            return R.ok(result);

        } catch (Exception e) {
            result.put("status", "error");
            result.put("error", e.getMessage());
            return R.fail("检查索引状态失败: " + e.getMessage());
        } finally {
            client.shutdown();
        }
    }

    @GetMapping("/init")
    public R<String> init() {
        // 初始化ES索引与字典过滤器
        var client = org.ruoyi.system.util.Es.ElasticsearchIndexUtil.createClient();
        try {
            novalForYhService.init(client);
            return R.ok("初始化完成");
        } catch (Exception e) {
            System.err.println("初始化失败: " + e.getMessage());
            // 检查是否是ES磁盘空间不足的错误
            if (e.getMessage().contains("TOO_MANY_REQUESTS") || e.getMessage().contains("disk usage exceeded")) {
                return R.ok("ES磁盘空间不足，但系统可正常运行（将使用数据库查询）");
            } else {
                return R.fail("初始化失败: " + e.getMessage());
            }
        } finally {
            client.shutdown();
        }
    }
}
