{"doc": " 小说目录\n\n <AUTHOR> \n @date 2025-06-07\n", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["org.ruoyi.system.domain.bo.NovalDirectoryBo", "org.ruoyi.core.page.PageQuery"], "doc": " 查询小说目录列表\n"}, {"name": "export", "paramTypes": ["org.ruoyi.system.domain.bo.NovalDirectoryBo", "jakarta.servlet.http.HttpServletResponse"], "doc": " 导出小说目录列表\n"}, {"name": "getInfo", "paramTypes": ["java.lang.String"], "doc": " 获取小说目录详细信息\n\n @param createDept 主键\n"}, {"name": "add", "paramTypes": ["org.ruoyi.system.domain.bo.NovalDirectoryBo"], "doc": " 新增小说目录\n"}, {"name": "edit", "paramTypes": ["org.ruoyi.system.domain.bo.NovalDirectoryBo"], "doc": " 修改小说目录\n"}, {"name": "remove", "paramTypes": ["java.lang.String[]"], "doc": " 删除小说目录\n\n @param createDepts 主键串\n"}, {"name": "removeforone", "paramTypes": ["java.lang.String[]", "java.lang.String[]"], "doc": " 删除小说单个章节\n\n @param createDepts 书本id\n @param chapter 章节名称\n"}], "constructors": []}