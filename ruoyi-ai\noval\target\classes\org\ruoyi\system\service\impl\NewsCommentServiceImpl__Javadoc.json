{"doc": " 消息跳转论坛Service业务层处理\n\n <AUTHOR>\n @date 2025-07-18\n", "fields": [], "enumConstants": [], "methods": [{"name": "queryById", "paramTypes": ["java.lang.Long"], "doc": " 查询消息跳转论坛\n"}, {"name": "queryPageList", "paramTypes": ["org.ruoyi.system.domain.bo.NewsCommentBo", "org.ruoyi.core.page.PageQuery"], "doc": " 查询消息跳转论坛列表\n"}, {"name": "queryList", "paramTypes": ["org.ruoyi.system.domain.bo.NewsCommentBo"], "doc": " 查询消息跳转论坛列表\n"}, {"name": "insertByBo", "paramTypes": ["org.ruoyi.system.domain.bo.NewsCommentBo"], "doc": " 新增消息跳转论坛\n"}, {"name": "updateByBo", "paramTypes": ["org.ruoyi.system.domain.bo.NewsCommentBo"], "doc": " 修改消息跳转论坛\n"}, {"name": "validEntityBeforeSave", "paramTypes": ["org.ruoyi.system.domain.NewsComment"], "doc": " 保存前的数据校验\n"}, {"name": "deleteWithValidByIds", "paramTypes": ["java.util.Collection", "java.lang.Bo<PERSON>an"], "doc": " 批量删除消息跳转论坛\n"}], "constructors": []}