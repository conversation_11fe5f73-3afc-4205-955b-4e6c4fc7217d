package org.ruoyi.system.domain.vo;

import javax.annotation.processing.Generated;
import org.ruoyi.system.domain.CommentGoodbad;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-12T10:45:13+0800",
    comments = "version: 1.5.3.Final, compiler: javac, environment: Java 17.0.12 (Oracle Corporation)"
)
@Component
public class CommentGoodbadVoToCommentGoodbadMapperImpl implements CommentGoodbadVoToCommentGoodbadMapper {

    @Override
    public CommentGoodbad convert(CommentGoodbadVo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        CommentGoodbad commentGoodbad = new CommentGoodbad();

        commentGoodbad.setCommerntId( arg0.getCommerntId() );
        commentGoodbad.setWhoId( arg0.getWhoId() );
        commentGoodbad.setGoodBad( arg0.getGoodBad() );

        return commentGoodbad;
    }

    @Override
    public CommentGoodbad convert(CommentGoodbadVo arg0, CommentGoodbad arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setCommerntId( arg0.getCommerntId() );
        arg1.setWhoId( arg0.getWhoId() );
        arg1.setGoodBad( arg0.getGoodBad() );

        return arg1;
    }
}
