package org.ruoyi.system.domain.bo;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.ruoyi.system.domain.Live2d2;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-12T10:45:13+0800",
    comments = "version: 1.5.3.Final, compiler: javac, environment: Java 17.0.12 (Oracle Corporation)"
)
@Component
public class Live2dBo2ToLive2d2MapperImpl implements Live2dBo2ToLive2d2Mapper {

    @Override
    public Live2d2 convert(Live2dBo2 arg0) {
        if ( arg0 == null ) {
            return null;
        }

        Live2d2 live2d2 = new Live2d2();

        live2d2.setSearchValue( arg0.getSearchValue() );
        live2d2.setCreateDept( arg0.getCreateDept() );
        live2d2.setCreateBy( arg0.getCreateBy() );
        live2d2.setCreateTime( arg0.getCreateTime() );
        live2d2.setUpdateBy( arg0.getUpdateBy() );
        live2d2.setUpdateTime( arg0.getUpdateTime() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            live2d2.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        live2d2.setId( arg0.getId() );
        live2d2.setName( arg0.getName() );
        live2d2.setType( arg0.getType() );
        List<String> list = arg0.getOriginalName();
        if ( list != null ) {
            live2d2.setOriginalName( new ArrayList<String>( list ) );
        }

        return live2d2;
    }

    @Override
    public Live2d2 convert(Live2dBo2 arg0, Live2d2 arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setSearchValue( arg0.getSearchValue() );
        arg1.setCreateDept( arg0.getCreateDept() );
        arg1.setCreateBy( arg0.getCreateBy() );
        arg1.setCreateTime( arg0.getCreateTime() );
        arg1.setUpdateBy( arg0.getUpdateBy() );
        arg1.setUpdateTime( arg0.getUpdateTime() );
        if ( arg1.getParams() != null ) {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.getParams().clear();
                arg1.getParams().putAll( map );
            }
            else {
                arg1.setParams( null );
            }
        }
        else {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.setParams( new LinkedHashMap<String, Object>( map ) );
            }
        }
        arg1.setId( arg0.getId() );
        arg1.setName( arg0.getName() );
        arg1.setType( arg0.getType() );
        if ( arg1.getOriginalName() != null ) {
            List<String> list = arg0.getOriginalName();
            if ( list != null ) {
                arg1.getOriginalName().clear();
                arg1.getOriginalName().addAll( list );
            }
            else {
                arg1.setOriginalName( null );
            }
        }
        else {
            List<String> list = arg0.getOriginalName();
            if ( list != null ) {
                arg1.setOriginalName( new ArrayList<String>( list ) );
            }
        }

        return arg1;
    }
}
