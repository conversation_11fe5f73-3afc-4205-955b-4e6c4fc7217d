package org.ruoyi.system.domain.vo;

import javax.annotation.processing.Generated;
import org.ruoyi.system.domain.Attention;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-12T10:45:13+0800",
    comments = "version: 1.5.3.Final, compiler: javac, environment: Java 17.0.12 (Oracle Corporation)"
)
@Component
public class AttentionVoToAttentionMapperImpl implements AttentionVoToAttentionMapper {

    @Override
    public Attention convert(AttentionVo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        Attention attention = new Attention();

        attention.setCreateTime( arg0.getCreateTime() );
        attention.setId( arg0.getId() );
        attention.setOtherId( arg0.getOtherId() );

        return attention;
    }

    @Override
    public Attention convert(AttentionVo arg0, Attention arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setCreateTime( arg0.getCreateTime() );
        arg1.setId( arg0.getId() );
        arg1.setOtherId( arg0.getOtherId() );

        return arg1;
    }
}
