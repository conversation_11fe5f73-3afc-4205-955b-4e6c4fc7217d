{"doc": " 评论Service业务层处理\n\n <AUTHOR>\n @date 2025-06-13\n", "fields": [], "enumConstants": [], "methods": [{"name": "queryLandlordById", "paramTypes": ["java.lang.Long"], "doc": " 查询评论\n"}, {"name": "queryPageList", "paramTypes": ["org.ruoyi.system.domain.bo.CommentBo", "org.ruoyi.core.page.PageQuery"], "doc": " 查询评论列表\n"}, {"name": "queryList", "paramTypes": ["org.ruoyi.system.domain.bo.CommentBo"], "doc": " 查询评论列表(非分页)\n"}, {"name": "insertByBo", "paramTypes": ["org.ruoyi.system.domain.bo.CommentBo"], "doc": " 新增评论\n"}, {"name": "updateByBo", "paramTypes": ["org.ruoyi.system.domain.bo.CommentBo"], "doc": " 修改评论\n"}, {"name": "validEntityBeforeSave", "paramTypes": ["org.ruoyi.system.domain.Comment"], "doc": " 保存前的数据校验\n"}, {"name": "deleteWithValidByIds", "paramTypes": ["java.util.Collection", "java.lang.Bo<PERSON>an"], "doc": " 批量删除评论\n"}, {"name": "loadallCache", "paramTypes": ["org.ruoyi.system.domain.bo.CommentBo"], "doc": " 非表单查询\n @param bo\n @return\n"}], "constructors": []}