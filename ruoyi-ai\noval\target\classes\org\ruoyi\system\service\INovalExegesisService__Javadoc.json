{"doc": " 批注Service接口\n\n <AUTHOR>\n @date 2025-07-01\n", "fields": [], "enumConstants": [], "methods": [{"name": "queryById", "paramTypes": ["java.lang.Long"], "doc": " 查询批注\n"}, {"name": "queryPageList", "paramTypes": ["org.ruoyi.system.domain.bo.NovalExegesisBo", "org.ruoyi.core.page.PageQuery"], "doc": " 查询批注列表\n"}, {"name": "queryList", "paramTypes": ["org.ruoyi.system.domain.bo.NovalExegesisBo"], "doc": " 查询批注列表\n"}, {"name": "insertByBo", "paramTypes": ["org.ruoyi.system.domain.bo.NovalExegesisBo"], "doc": " 新增批注\n"}, {"name": "updateByBo", "paramTypes": ["org.ruoyi.system.domain.bo.NovalExegesisBo"], "doc": " 修改批注\n"}, {"name": "deleteWithValidByIds", "paramTypes": ["java.util.Collection", "java.lang.Bo<PERSON>an"], "doc": " 校验并批量删除批注信息\n"}], "constructors": []}