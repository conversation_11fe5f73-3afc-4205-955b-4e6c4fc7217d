package org.ruoyi.system.domain.bo;

import java.util.LinkedHashMap;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.ruoyi.system.domain.Mynoval;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-12T10:45:13+0800",
    comments = "version: 1.5.3.Final, compiler: javac, environment: Java 17.0.12 (Oracle Corporation)"
)
@Component
public class MynovalBoToMynovalMapperImpl implements MynovalBoToMynovalMapper {

    @Override
    public Mynoval convert(MynovalBo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        Mynoval mynoval = new Mynoval();

        mynoval.setSearchValue( arg0.getSearchValue() );
        mynoval.setCreateDept( arg0.getCreateDept() );
        mynoval.setCreateBy( arg0.getCreateBy() );
        mynoval.setCreateTime( arg0.getCreateTime() );
        mynoval.setUpdateBy( arg0.getUpdateBy() );
        mynoval.setUpdateTime( arg0.getUpdateTime() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            mynoval.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        mynoval.setId( arg0.getId() );
        mynoval.setUserid( arg0.getUserid() );
        mynoval.setCosid( arg0.getCosid() );

        return mynoval;
    }

    @Override
    public Mynoval convert(MynovalBo arg0, Mynoval arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setSearchValue( arg0.getSearchValue() );
        arg1.setCreateDept( arg0.getCreateDept() );
        arg1.setCreateBy( arg0.getCreateBy() );
        arg1.setCreateTime( arg0.getCreateTime() );
        arg1.setUpdateBy( arg0.getUpdateBy() );
        arg1.setUpdateTime( arg0.getUpdateTime() );
        if ( arg1.getParams() != null ) {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.getParams().clear();
                arg1.getParams().putAll( map );
            }
            else {
                arg1.setParams( null );
            }
        }
        else {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.setParams( new LinkedHashMap<String, Object>( map ) );
            }
        }
        arg1.setId( arg0.getId() );
        arg1.setUserid( arg0.getUserid() );
        arg1.setCosid( arg0.getCosid() );

        return arg1;
    }
}
