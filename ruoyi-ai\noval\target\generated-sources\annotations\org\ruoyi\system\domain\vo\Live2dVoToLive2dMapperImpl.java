package org.ruoyi.system.domain.vo;

import javax.annotation.processing.Generated;
import org.ruoyi.system.domain.Live2d;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-12T10:45:13+0800",
    comments = "version: 1.5.3.Final, compiler: javac, environment: Java 17.0.12 (Oracle Corporation)"
)
@Component
public class Live2dVoToLive2dMapperImpl implements Live2dVoToLive2dMapper {

    @Override
    public Live2d convert(Live2dVo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        Live2d live2d = new Live2d();

        live2d.setId( arg0.getId() );
        live2d.setName( arg0.getName() );
        live2d.setType( arg0.getType() );
        live2d.setOriginalName( arg0.getOriginalName() );

        return live2d;
    }

    @Override
    public Live2d convert(Live2dVo arg0, Live2d arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setName( arg0.getName() );
        arg1.setType( arg0.getType() );
        arg1.setOriginalName( arg0.getOriginalName() );

        return arg1;
    }
}
