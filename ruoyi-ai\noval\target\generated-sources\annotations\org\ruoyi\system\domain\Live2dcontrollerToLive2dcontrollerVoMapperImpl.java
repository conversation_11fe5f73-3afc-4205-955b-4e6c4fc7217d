package org.ruoyi.system.domain;

import javax.annotation.processing.Generated;
import org.ruoyi.system.domain.vo.Live2dcontrollerVo;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-12T10:45:13+0800",
    comments = "version: 1.5.3.Final, compiler: javac, environment: Java 17.0.12 (Oracle Corporation)"
)
@Component
public class Live2dcontrollerToLive2dcontrollerVoMapperImpl implements Live2dcontrollerToLive2dcontrollerVoMapper {

    @Override
    public Live2dcontrollerVo convert(Live2dcontroller arg0) {
        if ( arg0 == null ) {
            return null;
        }

        Live2dcontrollerVo live2dcontrollerVo = new Live2dcontrollerVo();

        live2dcontrollerVo.setId( arg0.getId() );
        live2dcontrollerVo.setUserid( arg0.getUserid() );
        live2dcontrollerVo.setName( arg0.getName() );

        return live2dcontrollerVo;
    }

    @Override
    public Live2dcontrollerVo convert(Live2dcontroller arg0, Live2dcontrollerVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setUserid( arg0.getUserid() );
        arg1.setName( arg0.getName() );

        return arg1;
    }
}
