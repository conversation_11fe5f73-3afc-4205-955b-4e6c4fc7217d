package org.ruoyi.system.domain;

import java.text.SimpleDateFormat;
import javax.annotation.processing.Generated;
import org.ruoyi.system.domain.vo.CommentVo;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-12T10:45:13+0800",
    comments = "version: 1.5.3.Final, compiler: javac, environment: Java 17.0.12 (Oracle Corporation)"
)
@Component
public class CommentToCommentVoMapperImpl implements CommentToCommentVoMapper {

    @Override
    public CommentVo convert(Comment arg0) {
        if ( arg0 == null ) {
            return null;
        }

        CommentVo commentVo = new CommentVo();

        commentVo.setCommerntId( arg0.getCommerntId() );
        commentVo.setMyId( arg0.getMyId() );
        commentVo.setCommerntType( arg0.getCommerntType() );
        commentVo.setIsResponse( arg0.getIsResponse() );
        commentVo.setResponseId( arg0.getResponseId() );
        commentVo.setContent( arg0.getContent() );
        commentVo.setLook( arg0.getLook() );
        commentVo.setIsFine( arg0.getIsFine() );
        commentVo.setIcon1( arg0.getIcon1() );
        commentVo.setIcon2( arg0.getIcon2() );
        commentVo.setIcon3( arg0.getIcon3() );
        commentVo.setIcon4( arg0.getIcon4() );
        commentVo.setIcon5( arg0.getIcon5() );
        commentVo.setFatherid( arg0.getFatherid() );
        if ( arg0.getCreateTime() != null ) {
            commentVo.setCreateTime( new SimpleDateFormat().format( arg0.getCreateTime() ) );
        }

        return commentVo;
    }

    @Override
    public CommentVo convert(Comment arg0, CommentVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setCommerntId( arg0.getCommerntId() );
        arg1.setMyId( arg0.getMyId() );
        arg1.setCommerntType( arg0.getCommerntType() );
        arg1.setIsResponse( arg0.getIsResponse() );
        arg1.setResponseId( arg0.getResponseId() );
        arg1.setContent( arg0.getContent() );
        arg1.setLook( arg0.getLook() );
        arg1.setIsFine( arg0.getIsFine() );
        arg1.setIcon1( arg0.getIcon1() );
        arg1.setIcon2( arg0.getIcon2() );
        arg1.setIcon3( arg0.getIcon3() );
        arg1.setIcon4( arg0.getIcon4() );
        arg1.setIcon5( arg0.getIcon5() );
        arg1.setFatherid( arg0.getFatherid() );
        if ( arg0.getCreateTime() != null ) {
            arg1.setCreateTime( new SimpleDateFormat().format( arg0.getCreateTime() ) );
        }
        else {
            arg1.setCreateTime( null );
        }

        return arg1;
    }
}
