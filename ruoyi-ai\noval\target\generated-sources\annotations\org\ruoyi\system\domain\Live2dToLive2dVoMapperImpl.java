package org.ruoyi.system.domain;

import javax.annotation.processing.Generated;
import org.ruoyi.system.domain.vo.Live2dVo;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-12T10:45:13+0800",
    comments = "version: 1.5.3.Final, compiler: javac, environment: Java 17.0.12 (Oracle Corporation)"
)
@Component
public class Live2dToLive2dVoMapperImpl implements Live2dToLive2dVoMapper {

    @Override
    public Live2dVo convert(Live2d arg0) {
        if ( arg0 == null ) {
            return null;
        }

        Live2dVo live2dVo = new Live2dVo();

        live2dVo.setId( arg0.getId() );
        live2dVo.setName( arg0.getName() );
        live2dVo.setType( arg0.getType() );
        live2dVo.setOriginalName( arg0.getOriginalName() );

        return live2dVo;
    }

    @Override
    public Live2dVo convert(Live2d arg0, Live2dVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setName( arg0.getName() );
        arg1.setType( arg0.getType() );
        arg1.setOriginalName( arg0.getOriginalName() );

        return arg1;
    }
}
