package org.ruoyi.system.domain.bo;

import java.util.LinkedHashMap;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.ruoyi.system.domain.Attention;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-12T10:45:12+0800",
    comments = "version: 1.5.3.Final, compiler: javac, environment: Java 17.0.12 (Oracle Corporation)"
)
@Component
public class AttentionBoToAttentionMapperImpl implements AttentionBoToAttentionMapper {

    @Override
    public Attention convert(AttentionBo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        Attention attention = new Attention();

        attention.setSearchValue( arg0.getSearchValue() );
        attention.setCreateDept( arg0.getCreateDept() );
        attention.setCreateBy( arg0.getCreateBy() );
        attention.setCreateTime( arg0.getCreateTime() );
        attention.setUpdateBy( arg0.getUpdateBy() );
        attention.setUpdateTime( arg0.getUpdateTime() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            attention.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        attention.setId( arg0.getId() );
        attention.setOtherId( arg0.getOtherId() );

        return attention;
    }

    @Override
    public Attention convert(AttentionBo arg0, Attention arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setSearchValue( arg0.getSearchValue() );
        arg1.setCreateDept( arg0.getCreateDept() );
        arg1.setCreateBy( arg0.getCreateBy() );
        arg1.setCreateTime( arg0.getCreateTime() );
        arg1.setUpdateBy( arg0.getUpdateBy() );
        arg1.setUpdateTime( arg0.getUpdateTime() );
        if ( arg1.getParams() != null ) {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.getParams().clear();
                arg1.getParams().putAll( map );
            }
            else {
                arg1.setParams( null );
            }
        }
        else {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.setParams( new LinkedHashMap<String, Object>( map ) );
            }
        }
        arg1.setId( arg0.getId() );
        arg1.setOtherId( arg0.getOtherId() );

        return arg1;
    }
}
