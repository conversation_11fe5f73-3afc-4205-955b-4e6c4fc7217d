package org.ruoyi.system.domain.vo;

import javax.annotation.processing.Generated;
import org.ruoyi.system.domain.NovalExegesis;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-12T10:45:13+0800",
    comments = "version: 1.5.3.Final, compiler: javac, environment: Java 17.0.12 (Oracle Corporation)"
)
@Component
public class NovalExegesisVoToNovalExegesisMapperImpl implements NovalExegesisVoToNovalExegesisMapper {

    @Override
    public NovalExegesis convert(NovalExegesisVo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        NovalExegesis novalExegesis = new NovalExegesis();

        novalExegesis.setId( arg0.getId() );
        novalExegesis.setUserid( arg0.getUserid() );
        novalExegesis.setNovalName( arg0.getNovalName() );
        novalExegesis.setNovalChapter( arg0.getNovalChapter() );
        novalExegesis.setNovalContent( arg0.getNovalContent() );
        novalExegesis.setExegesis( arg0.getExegesis() );
        novalExegesis.setNovalId( arg0.getNovalId() );

        return novalExegesis;
    }

    @Override
    public NovalExegesis convert(NovalExegesisVo arg0, NovalExegesis arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setUserid( arg0.getUserid() );
        arg1.setNovalName( arg0.getNovalName() );
        arg1.setNovalChapter( arg0.getNovalChapter() );
        arg1.setNovalContent( arg0.getNovalContent() );
        arg1.setExegesis( arg0.getExegesis() );
        arg1.setNovalId( arg0.getNovalId() );

        return arg1;
    }
}
