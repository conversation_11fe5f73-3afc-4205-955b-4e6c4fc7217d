{"doc": " 小说详细Service业务层处理\n\n <AUTHOR> \n @date 2025-06-07\n", "fields": [], "enumConstants": [], "methods": [{"name": "queryById", "paramTypes": ["java.lang.Long"], "doc": " 查询小说详细\n"}, {"name": "queryPageList", "paramTypes": ["org.ruoyi.system.domain.bo.NovalDetailsBo", "org.ruoyi.core.page.PageQuery"], "doc": " 查询小说详细列表\n"}, {"name": "queryList", "paramTypes": ["org.ruoyi.system.domain.bo.NovalDetailsBo"], "doc": " 查询小说详细列表\n"}, {"name": "insertByBo", "paramTypes": ["org.ruoyi.system.domain.bo.NovalDetailsBo"], "doc": " 新增小说详细\n"}, {"name": "updateByBo", "paramTypes": ["org.ruoyi.system.domain.bo.NovalDetailsBo"], "doc": " 修改小说详细\n"}, {"name": "validEntityBeforeSave", "paramTypes": ["org.ruoyi.system.domain.NovalDetails"], "doc": " 保存前的数据校验\n"}, {"name": "deleteWithValidByIds", "paramTypes": ["java.util.Collection", "java.lang.Bo<PERSON>an"], "doc": " 批量删除小说详细\n"}], "constructors": []}