{"doc": " 用户个性live2d控制Service接口\n\n <AUTHOR>\n @date 2025-06-24\n", "fields": [], "enumConstants": [], "methods": [{"name": "queryById", "paramTypes": ["java.lang.Long"], "doc": " 查询用户个性live2d控制\n"}, {"name": "queryPageList", "paramTypes": ["org.ruoyi.system.domain.bo.Live2dcontrollerBo", "org.ruoyi.core.page.PageQuery"], "doc": " 查询用户个性live2d控制列表\n"}, {"name": "queryList", "paramTypes": ["org.ruoyi.system.domain.bo.Live2dcontrollerBo"], "doc": " 查询用户个性live2d控制列表\n"}, {"name": "insertByBo", "paramTypes": ["org.ruoyi.system.domain.bo.Live2dcontrollerBo"], "doc": " 新增用户个性live2d控制\n"}, {"name": "updateByBo", "paramTypes": ["org.ruoyi.system.domain.bo.Live2dcontrollerBo"], "doc": " 修改用户个性live2d控制\n"}, {"name": "deleteWithValidByIds", "paramTypes": ["java.util.Collection", "java.lang.Bo<PERSON>an"], "doc": " 校验并批量删除用户个性live2d控制信息\n"}], "constructors": []}