{"doc": " 评论的点赞与踩Service接口\n\n <AUTHOR>\n @date 2025-06-16\n", "fields": [], "enumConstants": [], "methods": [{"name": "queryPageList", "paramTypes": ["org.ruoyi.system.domain.bo.CommentGoodbadBo", "org.ruoyi.core.page.PageQuery"], "doc": " 查询评论的点赞与踩列表\n"}, {"name": "queryList", "paramTypes": ["org.ruoyi.system.domain.bo.CommentGoodbadBo"], "doc": " 查询评论的点赞与踩列表\n"}, {"name": "insertByBo", "paramTypes": ["org.ruoyi.system.domain.bo.CommentGoodbadBo"], "doc": " 新增评论的点赞与踩\n"}, {"name": "updateByBo", "paramTypes": ["org.ruoyi.system.domain.bo.CommentGoodbadBo"], "doc": " 修改评论的点赞与踩\n"}, {"name": "deleteWithValidByIds", "paramTypes": ["java.util.Collection", "java.lang.Bo<PERSON>an"], "doc": " 校验并批量删除评论的点赞与踩信息\n"}], "constructors": []}