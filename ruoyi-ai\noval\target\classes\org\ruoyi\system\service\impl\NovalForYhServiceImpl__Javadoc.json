{"doc": " 用户浏览书库Service业务层处理\n\n <AUTHOR>\n @date 2025-06-11\n", "fields": [], "enumConstants": [], "methods": [{"name": "queryPageList", "paramTypes": ["org.ruoyi.system.domain.bo.NovalForYhBo", "org.ruoyi.core.page.PageQuery"], "doc": " 带缓存查询用户浏览书库列表\n"}, {"name": "loadDataAndCache", "paramTypes": ["java.lang.String", "org.ruoyi.system.domain.bo.NovalForYhBo", "org.ruoyi.core.page.PageQuery"], "doc": " 查询用户浏览书库列表\n"}, {"name": "loadDataAndCacheForSearch", "paramTypes": ["java.lang.String", "org.ruoyi.system.util.RedisDate", "org.ruoyi.system.domain.bo.NovalForYhBo"], "doc": " 查询用户浏览书库列表\n"}], "constructors": []}