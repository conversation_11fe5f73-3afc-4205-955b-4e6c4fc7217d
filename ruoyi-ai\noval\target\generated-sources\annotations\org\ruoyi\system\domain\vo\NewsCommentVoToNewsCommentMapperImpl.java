package org.ruoyi.system.domain.vo;

import javax.annotation.processing.Generated;
import org.ruoyi.system.domain.NewsComment;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-12T10:45:13+0800",
    comments = "version: 1.5.3.Final, compiler: javac, environment: Java 17.0.12 (Oracle Corporation)"
)
@Component
public class NewsCommentVoToNewsCommentMapperImpl implements NewsCommentVoToNewsCommentMapper {

    @Override
    public NewsComment convert(NewsCommentVo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        NewsComment newsComment = new NewsComment();

        newsComment.setNewsId( arg0.getNewsId() );
        newsComment.setCommerntId( arg0.getCommerntId() );

        return newsComment;
    }

    @Override
    public NewsComment convert(NewsCommentVo arg0, NewsComment arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setNewsId( arg0.getNewsId() );
        arg1.setCommerntId( arg0.getCommerntId() );

        return arg1;
    }
}
