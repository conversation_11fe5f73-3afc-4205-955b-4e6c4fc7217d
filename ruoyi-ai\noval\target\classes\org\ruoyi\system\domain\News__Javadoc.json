{"doc": " 聊天对象 news\n\n <AUTHOR>\n @date 2025-06-17\n", "fields": [{"name": "id", "doc": " id\n"}, {"name": "otherId", "doc": " 消息发起人id\n"}, {"name": "myId", "doc": " 消息接收者id\n"}, {"name": "news", "doc": " 消息内容\n"}, {"name": "type", "doc": " 消息类型0代表管理员信息，1代表论坛信息，2代表点赞消息，3代表私聊信息，4代表关注消息\n"}, {"name": "isread", "doc": " isread=0未未读，isread=1为已读。\n"}], "enumConstants": [], "methods": [], "constructors": []}