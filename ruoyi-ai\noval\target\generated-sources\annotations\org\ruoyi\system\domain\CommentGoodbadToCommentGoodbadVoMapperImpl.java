package org.ruoyi.system.domain;

import javax.annotation.processing.Generated;
import org.ruoyi.system.domain.vo.CommentGoodbadVo;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-12T10:45:13+0800",
    comments = "version: 1.5.3.Final, compiler: javac, environment: Java 17.0.12 (Oracle Corporation)"
)
@Component
public class CommentGoodbadToCommentGoodbadVoMapperImpl implements CommentGoodbadToCommentGoodbadVoMapper {

    @Override
    public CommentGoodbadVo convert(CommentGoodbad arg0) {
        if ( arg0 == null ) {
            return null;
        }

        CommentGoodbadVo commentGoodbadVo = new CommentGoodbadVo();

        commentGoodbadVo.setCommerntId( arg0.getCommerntId() );
        commentGoodbadVo.setWhoId( arg0.getWhoId() );
        commentGoodbadVo.setGoodBad( arg0.getGoodBad() );

        return commentGoodbadVo;
    }

    @Override
    public CommentGoodbadVo convert(CommentGoodbad arg0, CommentGoodbadVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setCommerntId( arg0.getCommerntId() );
        arg1.setWhoId( arg0.getWhoId() );
        arg1.setGoodBad( arg0.getGoodBad() );

        return arg1;
    }
}
