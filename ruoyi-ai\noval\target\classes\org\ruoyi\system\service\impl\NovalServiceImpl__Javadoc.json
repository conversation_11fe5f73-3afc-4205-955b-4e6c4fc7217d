{"doc": " 书库管理Service业务层处理\n\n <AUTHOR> \n @date 2025-06-06\n", "fields": [], "enumConstants": [], "methods": [{"name": "queryById", "paramTypes": ["java.lang.Long"], "doc": " 查询书库管理\n"}, {"name": "queryPageList", "paramTypes": ["org.ruoyi.system.domain.bo.NovalBo", "org.ruoyi.core.page.PageQuery"], "doc": " 查询书库管理列表\n"}, {"name": "queryList", "paramTypes": ["org.ruoyi.system.domain.bo.NovalBo"], "doc": " 查询书库管理列表\n"}, {"name": "insertByBo", "paramTypes": ["org.ruoyi.system.domain.bo.NovalBo"], "doc": " 新增书库管理\n"}, {"name": "updateByBo", "paramTypes": ["org.ruoyi.system.domain.bo.NovalBo"], "doc": " 修改书库管理\n"}, {"name": "validEntityBeforeSave", "paramTypes": ["org.ruoyi.system.domain.Noval"], "doc": " 保存前的数据校验\n"}, {"name": "deleteWithValidByIds", "paramTypes": ["java.util.Collection", "java.lang.Bo<PERSON>an"], "doc": " 批量删除书库管理\n"}, {"name": "weekAdd", "paramTypes": [], "doc": " 本周新增书籍\n"}], "constructors": []}