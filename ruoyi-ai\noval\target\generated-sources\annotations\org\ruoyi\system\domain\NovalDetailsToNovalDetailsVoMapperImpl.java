package org.ruoyi.system.domain;

import javax.annotation.processing.Generated;
import org.ruoyi.system.domain.vo.NovalDetailsVo;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-12T10:45:13+0800",
    comments = "version: 1.5.3.Final, compiler: javac, environment: Java 17.0.12 (Oracle Corporation)"
)
@Component
public class NovalDetailsToNovalDetailsVoMapperImpl implements NovalDetailsToNovalDetailsVoMapper {

    @Override
    public NovalDetailsVo convert(NovalDetails arg0) {
        if ( arg0 == null ) {
            return null;
        }

        NovalDetailsVo novalDetailsVo = new NovalDetailsVo();

        novalDetailsVo.setId( arg0.getId() );
        novalDetailsVo.setGrades( arg0.getGrades() );
        novalDetailsVo.setType( arg0.getType() );
        novalDetailsVo.setPlatform( arg0.getPlatform() );
        novalDetailsVo.setSummary( arg0.getSummary() );
        novalDetailsVo.setIcon( arg0.getIcon() );
        novalDetailsVo.setLook( arg0.getLook() );

        return novalDetailsVo;
    }

    @Override
    public NovalDetailsVo convert(NovalDetails arg0, NovalDetailsVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setGrades( arg0.getGrades() );
        arg1.setType( arg0.getType() );
        arg1.setPlatform( arg0.getPlatform() );
        arg1.setSummary( arg0.getSummary() );
        arg1.setIcon( arg0.getIcon() );
        arg1.setLook( arg0.getLook() );

        return arg1;
    }
}
