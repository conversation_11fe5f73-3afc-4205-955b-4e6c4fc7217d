package org.ruoyi.system.domain;

import javax.annotation.processing.Generated;
import org.ruoyi.system.domain.vo.MynovalVo;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-12T10:45:13+0800",
    comments = "version: 1.5.3.Final, compiler: javac, environment: Java 17.0.12 (Oracle Corporation)"
)
@Component
public class MynovalToMynovalVoMapperImpl implements MynovalToMynovalVoMapper {

    @Override
    public MynovalVo convert(Mynoval arg0) {
        if ( arg0 == null ) {
            return null;
        }

        MynovalVo mynovalVo = new MynovalVo();

        mynovalVo.setId( arg0.getId() );
        mynovalVo.setUserid( arg0.getUserid() );
        mynovalVo.setCosid( arg0.getCosid() );

        return mynovalVo;
    }

    @Override
    public MynovalVo convert(Mynoval arg0, MynovalVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setUserid( arg0.getUserid() );
        arg1.setCosid( arg0.getCosid() );

        return arg1;
    }
}
