package io.github.linpeilie;

import org.ruoyi.system.domain.Attention;
import org.ruoyi.system.domain.AttentionToAttentionVoMapper;
import org.ruoyi.system.domain.Comment;
import org.ruoyi.system.domain.CommentGoodbad;
import org.ruoyi.system.domain.CommentGoodbadToCommentGoodbadVoMapper;
import org.ruoyi.system.domain.CommentToCommentVoMapper;
import org.ruoyi.system.domain.Live2d;
import org.ruoyi.system.domain.Live2d2;
import org.ruoyi.system.domain.Live2dToLive2dVoMapper;
import org.ruoyi.system.domain.Live2dcontroller;
import org.ruoyi.system.domain.Live2dcontrollerToLive2dcontrollerVoMapper;
import org.ruoyi.system.domain.Mynoval;
import org.ruoyi.system.domain.MynovalToMynovalVoMapper;
import org.ruoyi.system.domain.News;
import org.ruoyi.system.domain.NewsComment;
import org.ruoyi.system.domain.NewsCommentToNewsCommentVoMapper;
import org.ruoyi.system.domain.NewsToNewsVoMapper;
import org.ruoyi.system.domain.Noval;
import org.ruoyi.system.domain.NovalDetails;
import org.ruoyi.system.domain.NovalDetailsToNovalDetailsVoMapper;
import org.ruoyi.system.domain.NovalDirectory;
import org.ruoyi.system.domain.NovalDirectoryToNovalDirectoryVoMapper;
import org.ruoyi.system.domain.NovalExegesis;
import org.ruoyi.system.domain.NovalExegesisToNovalExegesisVoMapper;
import org.ruoyi.system.domain.NovalForYh;
import org.ruoyi.system.domain.NovalForYhToNovalForYhVoMapper;
import org.ruoyi.system.domain.NovalToNovalVoMapper;
import org.ruoyi.system.domain.Today;
import org.ruoyi.system.domain.TodayToTodayVoMapper;
import org.ruoyi.system.domain.bo.AttentionBo;
import org.ruoyi.system.domain.bo.AttentionBoToAttentionMapper;
import org.ruoyi.system.domain.bo.CommentBo;
import org.ruoyi.system.domain.bo.CommentBoToCommentMapper;
import org.ruoyi.system.domain.bo.CommentGoodbadBo;
import org.ruoyi.system.domain.bo.CommentGoodbadBoToCommentGoodbadMapper;
import org.ruoyi.system.domain.bo.Live2dBo;
import org.ruoyi.system.domain.bo.Live2dBo2;
import org.ruoyi.system.domain.bo.Live2dBo2ToLive2d2Mapper;
import org.ruoyi.system.domain.bo.Live2dBoToLive2dMapper;
import org.ruoyi.system.domain.bo.Live2dcontrollerBo;
import org.ruoyi.system.domain.bo.Live2dcontrollerBoToLive2dcontrollerMapper;
import org.ruoyi.system.domain.bo.MynovalBo;
import org.ruoyi.system.domain.bo.MynovalBoToMynovalMapper;
import org.ruoyi.system.domain.bo.NewsBo;
import org.ruoyi.system.domain.bo.NewsBoToNewsMapper;
import org.ruoyi.system.domain.bo.NewsCommentBo;
import org.ruoyi.system.domain.bo.NewsCommentBoToNewsCommentMapper;
import org.ruoyi.system.domain.bo.NovalBo;
import org.ruoyi.system.domain.bo.NovalBoToNovalMapper;
import org.ruoyi.system.domain.bo.NovalDetailsBo;
import org.ruoyi.system.domain.bo.NovalDetailsBoToNovalDetailsMapper;
import org.ruoyi.system.domain.bo.NovalDirectoryBo;
import org.ruoyi.system.domain.bo.NovalDirectoryBoToNovalDirectoryMapper;
import org.ruoyi.system.domain.bo.NovalExegesisBo;
import org.ruoyi.system.domain.bo.NovalExegesisBoToNovalExegesisMapper;
import org.ruoyi.system.domain.bo.NovalForYhBo;
import org.ruoyi.system.domain.bo.NovalForYhBoToNovalForYhMapper;
import org.ruoyi.system.domain.bo.TodayBo;
import org.ruoyi.system.domain.bo.TodayBoToTodayMapper;
import org.ruoyi.system.domain.vo.AttentionVo;
import org.ruoyi.system.domain.vo.AttentionVoToAttentionMapper;
import org.ruoyi.system.domain.vo.CommentGoodbadVo;
import org.ruoyi.system.domain.vo.CommentGoodbadVoToCommentGoodbadMapper;
import org.ruoyi.system.domain.vo.CommentVo;
import org.ruoyi.system.domain.vo.CommentVoToCommentMapper;
import org.ruoyi.system.domain.vo.Live2dVo;
import org.ruoyi.system.domain.vo.Live2dVoToLive2dMapper;
import org.ruoyi.system.domain.vo.Live2dcontrollerVo;
import org.ruoyi.system.domain.vo.Live2dcontrollerVoToLive2dcontrollerMapper;
import org.ruoyi.system.domain.vo.MynovalVo;
import org.ruoyi.system.domain.vo.MynovalVoToMynovalMapper;
import org.ruoyi.system.domain.vo.NewsCommentVo;
import org.ruoyi.system.domain.vo.NewsCommentVoToNewsCommentMapper;
import org.ruoyi.system.domain.vo.NewsVo;
import org.ruoyi.system.domain.vo.NewsVoToNewsMapper;
import org.ruoyi.system.domain.vo.NovalDetailsVo;
import org.ruoyi.system.domain.vo.NovalDetailsVoToNovalDetailsMapper;
import org.ruoyi.system.domain.vo.NovalDirectoryVo;
import org.ruoyi.system.domain.vo.NovalDirectoryVoToNovalDirectoryMapper;
import org.ruoyi.system.domain.vo.NovalExegesisVo;
import org.ruoyi.system.domain.vo.NovalExegesisVoToNovalExegesisMapper;
import org.ruoyi.system.domain.vo.NovalForYhVo;
import org.ruoyi.system.domain.vo.NovalForYhVoToNovalForYhMapper;
import org.ruoyi.system.domain.vo.NovalVo;
import org.ruoyi.system.domain.vo.NovalVoToNovalMapper;
import org.ruoyi.system.domain.vo.TodayVo;
import org.ruoyi.system.domain.vo.TodayVoToTodayMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

@Component
public class ConvertMapperAdapter {
  private CommentToCommentVoMapper commentToCommentVoMapper;

  private NewsCommentBoToNewsCommentMapper newsCommentBoToNewsCommentMapper;

  private Live2dcontrollerToLive2dcontrollerVoMapper live2dcontrollerToLive2dcontrollerVoMapper;

  private NovalBoToNovalMapper novalBoToNovalMapper;

  private NovalExegesisVoToNovalExegesisMapper novalExegesisVoToNovalExegesisMapper;

  private MynovalToMynovalVoMapper mynovalToMynovalVoMapper;

  private NovalExegesisToNovalExegesisVoMapper novalExegesisToNovalExegesisVoMapper;

  private CommentVoToCommentMapper commentVoToCommentMapper;

  private NovalToNovalVoMapper novalToNovalVoMapper;

  private CommentGoodbadVoToCommentGoodbadMapper commentGoodbadVoToCommentGoodbadMapper;

  private Live2dBo2ToLive2d2Mapper live2dBo2ToLive2d2Mapper;

  private CommentBoToCommentMapper commentBoToCommentMapper;

  private NovalForYhVoToNovalForYhMapper novalForYhVoToNovalForYhMapper;

  private NewsCommentVoToNewsCommentMapper newsCommentVoToNewsCommentMapper;

  private NovalForYhToNovalForYhVoMapper novalForYhToNovalForYhVoMapper;

  private TodayToTodayVoMapper todayToTodayVoMapper;

  private NewsCommentToNewsCommentVoMapper newsCommentToNewsCommentVoMapper;

  private AttentionVoToAttentionMapper attentionVoToAttentionMapper;

  private Live2dBoToLive2dMapper live2dBoToLive2dMapper;

  private NovalForYhBoToNovalForYhMapper novalForYhBoToNovalForYhMapper;

  private CommentGoodbadToCommentGoodbadVoMapper commentGoodbadToCommentGoodbadVoMapper;

  private TodayBoToTodayMapper todayBoToTodayMapper;

  private NovalDetailsVoToNovalDetailsMapper novalDetailsVoToNovalDetailsMapper;

  private NovalVoToNovalMapper novalVoToNovalMapper;

  private Live2dToLive2dVoMapper live2dToLive2dVoMapper;

  private Live2dcontrollerVoToLive2dcontrollerMapper live2dcontrollerVoToLive2dcontrollerMapper;

  private NewsBoToNewsMapper newsBoToNewsMapper;

  private MynovalVoToMynovalMapper mynovalVoToMynovalMapper;

  private AttentionBoToAttentionMapper attentionBoToAttentionMapper;

  private Live2dVoToLive2dMapper live2dVoToLive2dMapper;

  private Live2dcontrollerBoToLive2dcontrollerMapper live2dcontrollerBoToLive2dcontrollerMapper;

  private NovalDirectoryBoToNovalDirectoryMapper novalDirectoryBoToNovalDirectoryMapper;

  private NovalDetailsBoToNovalDetailsMapper novalDetailsBoToNovalDetailsMapper;

  private NewsToNewsVoMapper newsToNewsVoMapper;

  private NovalDirectoryToNovalDirectoryVoMapper novalDirectoryToNovalDirectoryVoMapper;

  private MynovalBoToMynovalMapper mynovalBoToMynovalMapper;

  private NovalDirectoryVoToNovalDirectoryMapper novalDirectoryVoToNovalDirectoryMapper;

  private TodayVoToTodayMapper todayVoToTodayMapper;

  private CommentGoodbadBoToCommentGoodbadMapper commentGoodbadBoToCommentGoodbadMapper;

  private NovalExegesisBoToNovalExegesisMapper novalExegesisBoToNovalExegesisMapper;

  private NewsVoToNewsMapper newsVoToNewsMapper;

  private AttentionToAttentionVoMapper attentionToAttentionVoMapper;

  private NovalDetailsToNovalDetailsVoMapper novalDetailsToNovalDetailsVoMapper;

  @Autowired
  public void setCommentToCommentVoMapper(@Lazy CommentToCommentVoMapper commentToCommentVoMapper) {
    this.commentToCommentVoMapper = commentToCommentVoMapper;
  }

  @Autowired
  public void setNewsCommentBoToNewsCommentMapper(@Lazy NewsCommentBoToNewsCommentMapper newsCommentBoToNewsCommentMapper) {
    this.newsCommentBoToNewsCommentMapper = newsCommentBoToNewsCommentMapper;
  }

  @Autowired
  public void setLive2dcontrollerToLive2dcontrollerVoMapper(@Lazy Live2dcontrollerToLive2dcontrollerVoMapper live2dcontrollerToLive2dcontrollerVoMapper) {
    this.live2dcontrollerToLive2dcontrollerVoMapper = live2dcontrollerToLive2dcontrollerVoMapper;
  }

  @Autowired
  public void setNovalBoToNovalMapper(@Lazy NovalBoToNovalMapper novalBoToNovalMapper) {
    this.novalBoToNovalMapper = novalBoToNovalMapper;
  }

  @Autowired
  public void setNovalExegesisVoToNovalExegesisMapper(@Lazy NovalExegesisVoToNovalExegesisMapper novalExegesisVoToNovalExegesisMapper) {
    this.novalExegesisVoToNovalExegesisMapper = novalExegesisVoToNovalExegesisMapper;
  }

  @Autowired
  public void setMynovalToMynovalVoMapper(@Lazy MynovalToMynovalVoMapper mynovalToMynovalVoMapper) {
    this.mynovalToMynovalVoMapper = mynovalToMynovalVoMapper;
  }

  @Autowired
  public void setNovalExegesisToNovalExegesisVoMapper(@Lazy NovalExegesisToNovalExegesisVoMapper novalExegesisToNovalExegesisVoMapper) {
    this.novalExegesisToNovalExegesisVoMapper = novalExegesisToNovalExegesisVoMapper;
  }

  @Autowired
  public void setCommentVoToCommentMapper(@Lazy CommentVoToCommentMapper commentVoToCommentMapper) {
    this.commentVoToCommentMapper = commentVoToCommentMapper;
  }

  @Autowired
  public void setNovalToNovalVoMapper(@Lazy NovalToNovalVoMapper novalToNovalVoMapper) {
    this.novalToNovalVoMapper = novalToNovalVoMapper;
  }

  @Autowired
  public void setCommentGoodbadVoToCommentGoodbadMapper(@Lazy CommentGoodbadVoToCommentGoodbadMapper commentGoodbadVoToCommentGoodbadMapper) {
    this.commentGoodbadVoToCommentGoodbadMapper = commentGoodbadVoToCommentGoodbadMapper;
  }

  @Autowired
  public void setLive2dBo2ToLive2d2Mapper(@Lazy Live2dBo2ToLive2d2Mapper live2dBo2ToLive2d2Mapper) {
    this.live2dBo2ToLive2d2Mapper = live2dBo2ToLive2d2Mapper;
  }

  @Autowired
  public void setCommentBoToCommentMapper(@Lazy CommentBoToCommentMapper commentBoToCommentMapper) {
    this.commentBoToCommentMapper = commentBoToCommentMapper;
  }

  @Autowired
  public void setNovalForYhVoToNovalForYhMapper(@Lazy NovalForYhVoToNovalForYhMapper novalForYhVoToNovalForYhMapper) {
    this.novalForYhVoToNovalForYhMapper = novalForYhVoToNovalForYhMapper;
  }

  @Autowired
  public void setNewsCommentVoToNewsCommentMapper(@Lazy NewsCommentVoToNewsCommentMapper newsCommentVoToNewsCommentMapper) {
    this.newsCommentVoToNewsCommentMapper = newsCommentVoToNewsCommentMapper;
  }

  @Autowired
  public void setNovalForYhToNovalForYhVoMapper(@Lazy NovalForYhToNovalForYhVoMapper novalForYhToNovalForYhVoMapper) {
    this.novalForYhToNovalForYhVoMapper = novalForYhToNovalForYhVoMapper;
  }

  @Autowired
  public void setTodayToTodayVoMapper(@Lazy TodayToTodayVoMapper todayToTodayVoMapper) {
    this.todayToTodayVoMapper = todayToTodayVoMapper;
  }

  @Autowired
  public void setNewsCommentToNewsCommentVoMapper(@Lazy NewsCommentToNewsCommentVoMapper newsCommentToNewsCommentVoMapper) {
    this.newsCommentToNewsCommentVoMapper = newsCommentToNewsCommentVoMapper;
  }

  @Autowired
  public void setAttentionVoToAttentionMapper(@Lazy AttentionVoToAttentionMapper attentionVoToAttentionMapper) {
    this.attentionVoToAttentionMapper = attentionVoToAttentionMapper;
  }

  @Autowired
  public void setLive2dBoToLive2dMapper(@Lazy Live2dBoToLive2dMapper live2dBoToLive2dMapper) {
    this.live2dBoToLive2dMapper = live2dBoToLive2dMapper;
  }

  @Autowired
  public void setNovalForYhBoToNovalForYhMapper(@Lazy NovalForYhBoToNovalForYhMapper novalForYhBoToNovalForYhMapper) {
    this.novalForYhBoToNovalForYhMapper = novalForYhBoToNovalForYhMapper;
  }

  @Autowired
  public void setCommentGoodbadToCommentGoodbadVoMapper(@Lazy CommentGoodbadToCommentGoodbadVoMapper commentGoodbadToCommentGoodbadVoMapper) {
    this.commentGoodbadToCommentGoodbadVoMapper = commentGoodbadToCommentGoodbadVoMapper;
  }

  @Autowired
  public void setTodayBoToTodayMapper(@Lazy TodayBoToTodayMapper todayBoToTodayMapper) {
    this.todayBoToTodayMapper = todayBoToTodayMapper;
  }

  @Autowired
  public void setNovalDetailsVoToNovalDetailsMapper(@Lazy NovalDetailsVoToNovalDetailsMapper novalDetailsVoToNovalDetailsMapper) {
    this.novalDetailsVoToNovalDetailsMapper = novalDetailsVoToNovalDetailsMapper;
  }

  @Autowired
  public void setNovalVoToNovalMapper(@Lazy NovalVoToNovalMapper novalVoToNovalMapper) {
    this.novalVoToNovalMapper = novalVoToNovalMapper;
  }

  @Autowired
  public void setLive2dToLive2dVoMapper(@Lazy Live2dToLive2dVoMapper live2dToLive2dVoMapper) {
    this.live2dToLive2dVoMapper = live2dToLive2dVoMapper;
  }

  @Autowired
  public void setLive2dcontrollerVoToLive2dcontrollerMapper(@Lazy Live2dcontrollerVoToLive2dcontrollerMapper live2dcontrollerVoToLive2dcontrollerMapper) {
    this.live2dcontrollerVoToLive2dcontrollerMapper = live2dcontrollerVoToLive2dcontrollerMapper;
  }

  @Autowired
  public void setNewsBoToNewsMapper(@Lazy NewsBoToNewsMapper newsBoToNewsMapper) {
    this.newsBoToNewsMapper = newsBoToNewsMapper;
  }

  @Autowired
  public void setMynovalVoToMynovalMapper(@Lazy MynovalVoToMynovalMapper mynovalVoToMynovalMapper) {
    this.mynovalVoToMynovalMapper = mynovalVoToMynovalMapper;
  }

  @Autowired
  public void setAttentionBoToAttentionMapper(@Lazy AttentionBoToAttentionMapper attentionBoToAttentionMapper) {
    this.attentionBoToAttentionMapper = attentionBoToAttentionMapper;
  }

  @Autowired
  public void setLive2dVoToLive2dMapper(@Lazy Live2dVoToLive2dMapper live2dVoToLive2dMapper) {
    this.live2dVoToLive2dMapper = live2dVoToLive2dMapper;
  }

  @Autowired
  public void setLive2dcontrollerBoToLive2dcontrollerMapper(@Lazy Live2dcontrollerBoToLive2dcontrollerMapper live2dcontrollerBoToLive2dcontrollerMapper) {
    this.live2dcontrollerBoToLive2dcontrollerMapper = live2dcontrollerBoToLive2dcontrollerMapper;
  }

  @Autowired
  public void setNovalDirectoryBoToNovalDirectoryMapper(@Lazy NovalDirectoryBoToNovalDirectoryMapper novalDirectoryBoToNovalDirectoryMapper) {
    this.novalDirectoryBoToNovalDirectoryMapper = novalDirectoryBoToNovalDirectoryMapper;
  }

  @Autowired
  public void setNovalDetailsBoToNovalDetailsMapper(@Lazy NovalDetailsBoToNovalDetailsMapper novalDetailsBoToNovalDetailsMapper) {
    this.novalDetailsBoToNovalDetailsMapper = novalDetailsBoToNovalDetailsMapper;
  }

  @Autowired
  public void setNewsToNewsVoMapper(@Lazy NewsToNewsVoMapper newsToNewsVoMapper) {
    this.newsToNewsVoMapper = newsToNewsVoMapper;
  }

  @Autowired
  public void setNovalDirectoryToNovalDirectoryVoMapper(@Lazy NovalDirectoryToNovalDirectoryVoMapper novalDirectoryToNovalDirectoryVoMapper) {
    this.novalDirectoryToNovalDirectoryVoMapper = novalDirectoryToNovalDirectoryVoMapper;
  }

  @Autowired
  public void setMynovalBoToMynovalMapper(@Lazy MynovalBoToMynovalMapper mynovalBoToMynovalMapper) {
    this.mynovalBoToMynovalMapper = mynovalBoToMynovalMapper;
  }

  @Autowired
  public void setNovalDirectoryVoToNovalDirectoryMapper(@Lazy NovalDirectoryVoToNovalDirectoryMapper novalDirectoryVoToNovalDirectoryMapper) {
    this.novalDirectoryVoToNovalDirectoryMapper = novalDirectoryVoToNovalDirectoryMapper;
  }

  @Autowired
  public void setTodayVoToTodayMapper(@Lazy TodayVoToTodayMapper todayVoToTodayMapper) {
    this.todayVoToTodayMapper = todayVoToTodayMapper;
  }

  @Autowired
  public void setCommentGoodbadBoToCommentGoodbadMapper(@Lazy CommentGoodbadBoToCommentGoodbadMapper commentGoodbadBoToCommentGoodbadMapper) {
    this.commentGoodbadBoToCommentGoodbadMapper = commentGoodbadBoToCommentGoodbadMapper;
  }

  @Autowired
  public void setNovalExegesisBoToNovalExegesisMapper(@Lazy NovalExegesisBoToNovalExegesisMapper novalExegesisBoToNovalExegesisMapper) {
    this.novalExegesisBoToNovalExegesisMapper = novalExegesisBoToNovalExegesisMapper;
  }

  @Autowired
  public void setNewsVoToNewsMapper(@Lazy NewsVoToNewsMapper newsVoToNewsMapper) {
    this.newsVoToNewsMapper = newsVoToNewsMapper;
  }

  @Autowired
  public void setAttentionToAttentionVoMapper(@Lazy AttentionToAttentionVoMapper attentionToAttentionVoMapper) {
    this.attentionToAttentionVoMapper = attentionToAttentionVoMapper;
  }

  @Autowired
  public void setNovalDetailsToNovalDetailsVoMapper(@Lazy NovalDetailsToNovalDetailsVoMapper novalDetailsToNovalDetailsVoMapper) {
    this.novalDetailsToNovalDetailsVoMapper = novalDetailsToNovalDetailsVoMapper;
  }

  public CommentVo commentToCommentVo(Comment comment) {
    return commentToCommentVoMapper.convert(comment);
  }

  public NewsComment newsCommentBoToNewsComment(NewsCommentBo newsCommentBo) {
    return newsCommentBoToNewsCommentMapper.convert(newsCommentBo);
  }

  public Live2dcontrollerVo live2dcontrollerToLive2dcontrollerVo(Live2dcontroller live2dcontroller) {
    return live2dcontrollerToLive2dcontrollerVoMapper.convert(live2dcontroller);
  }

  public Noval novalBoToNoval(NovalBo novalBo) {
    return novalBoToNovalMapper.convert(novalBo);
  }

  public NovalExegesis novalExegesisVoToNovalExegesis(NovalExegesisVo novalExegesisVo) {
    return novalExegesisVoToNovalExegesisMapper.convert(novalExegesisVo);
  }

  public MynovalVo mynovalToMynovalVo(Mynoval mynoval) {
    return mynovalToMynovalVoMapper.convert(mynoval);
  }

  public NovalExegesisVo novalExegesisToNovalExegesisVo(NovalExegesis novalExegesis) {
    return novalExegesisToNovalExegesisVoMapper.convert(novalExegesis);
  }

  public Comment commentVoToComment(CommentVo commentVo) {
    return commentVoToCommentMapper.convert(commentVo);
  }

  public NovalVo novalToNovalVo(Noval noval) {
    return novalToNovalVoMapper.convert(noval);
  }

  public CommentGoodbad commentGoodbadVoToCommentGoodbad(CommentGoodbadVo commentGoodbadVo) {
    return commentGoodbadVoToCommentGoodbadMapper.convert(commentGoodbadVo);
  }

  public Live2d2 live2dBo2ToLive2d2(Live2dBo2 live2dBo2) {
    return live2dBo2ToLive2d2Mapper.convert(live2dBo2);
  }

  public Comment commentBoToComment(CommentBo commentBo) {
    return commentBoToCommentMapper.convert(commentBo);
  }

  public NovalForYh novalForYhVoToNovalForYh(NovalForYhVo novalForYhVo) {
    return novalForYhVoToNovalForYhMapper.convert(novalForYhVo);
  }

  public NewsComment newsCommentVoToNewsComment(NewsCommentVo newsCommentVo) {
    return newsCommentVoToNewsCommentMapper.convert(newsCommentVo);
  }

  public NovalForYhVo novalForYhToNovalForYhVo(NovalForYh novalForYh) {
    return novalForYhToNovalForYhVoMapper.convert(novalForYh);
  }

  public TodayVo todayToTodayVo(Today today) {
    return todayToTodayVoMapper.convert(today);
  }

  public NewsCommentVo newsCommentToNewsCommentVo(NewsComment newsComment) {
    return newsCommentToNewsCommentVoMapper.convert(newsComment);
  }

  public Attention attentionVoToAttention(AttentionVo attentionVo) {
    return attentionVoToAttentionMapper.convert(attentionVo);
  }

  public Live2d live2dBoToLive2d(Live2dBo live2dBo) {
    return live2dBoToLive2dMapper.convert(live2dBo);
  }

  public NovalForYh novalForYhBoToNovalForYh(NovalForYhBo novalForYhBo) {
    return novalForYhBoToNovalForYhMapper.convert(novalForYhBo);
  }

  public CommentGoodbadVo commentGoodbadToCommentGoodbadVo(CommentGoodbad commentGoodbad) {
    return commentGoodbadToCommentGoodbadVoMapper.convert(commentGoodbad);
  }

  public Today todayBoToToday(TodayBo todayBo) {
    return todayBoToTodayMapper.convert(todayBo);
  }

  public NovalDetails novalDetailsVoToNovalDetails(NovalDetailsVo novalDetailsVo) {
    return novalDetailsVoToNovalDetailsMapper.convert(novalDetailsVo);
  }

  public Noval novalVoToNoval(NovalVo novalVo) {
    return novalVoToNovalMapper.convert(novalVo);
  }

  public Live2dVo live2dToLive2dVo(Live2d live2d) {
    return live2dToLive2dVoMapper.convert(live2d);
  }

  public Live2dcontroller live2dcontrollerVoToLive2dcontroller(Live2dcontrollerVo live2dcontrollerVo) {
    return live2dcontrollerVoToLive2dcontrollerMapper.convert(live2dcontrollerVo);
  }

  public News newsBoToNews(NewsBo newsBo) {
    return newsBoToNewsMapper.convert(newsBo);
  }

  public Mynoval mynovalVoToMynoval(MynovalVo mynovalVo) {
    return mynovalVoToMynovalMapper.convert(mynovalVo);
  }

  public Attention attentionBoToAttention(AttentionBo attentionBo) {
    return attentionBoToAttentionMapper.convert(attentionBo);
  }

  public Live2d live2dVoToLive2d(Live2dVo live2dVo) {
    return live2dVoToLive2dMapper.convert(live2dVo);
  }

  public Live2dcontroller live2dcontrollerBoToLive2dcontroller(Live2dcontrollerBo live2dcontrollerBo) {
    return live2dcontrollerBoToLive2dcontrollerMapper.convert(live2dcontrollerBo);
  }

  public NovalDirectory novalDirectoryBoToNovalDirectory(NovalDirectoryBo novalDirectoryBo) {
    return novalDirectoryBoToNovalDirectoryMapper.convert(novalDirectoryBo);
  }

  public NovalDetails novalDetailsBoToNovalDetails(NovalDetailsBo novalDetailsBo) {
    return novalDetailsBoToNovalDetailsMapper.convert(novalDetailsBo);
  }

  public NewsVo newsToNewsVo(News news) {
    return newsToNewsVoMapper.convert(news);
  }

  public NovalDirectoryVo novalDirectoryToNovalDirectoryVo(NovalDirectory novalDirectory) {
    return novalDirectoryToNovalDirectoryVoMapper.convert(novalDirectory);
  }

  public Mynoval mynovalBoToMynoval(MynovalBo mynovalBo) {
    return mynovalBoToMynovalMapper.convert(mynovalBo);
  }

  public NovalDirectory novalDirectoryVoToNovalDirectory(NovalDirectoryVo novalDirectoryVo) {
    return novalDirectoryVoToNovalDirectoryMapper.convert(novalDirectoryVo);
  }

  public Today todayVoToToday(TodayVo todayVo) {
    return todayVoToTodayMapper.convert(todayVo);
  }

  public CommentGoodbad commentGoodbadBoToCommentGoodbad(CommentGoodbadBo commentGoodbadBo) {
    return commentGoodbadBoToCommentGoodbadMapper.convert(commentGoodbadBo);
  }

  public NovalExegesis novalExegesisBoToNovalExegesis(NovalExegesisBo novalExegesisBo) {
    return novalExegesisBoToNovalExegesisMapper.convert(novalExegesisBo);
  }

  public News newsVoToNews(NewsVo newsVo) {
    return newsVoToNewsMapper.convert(newsVo);
  }

  public AttentionVo attentionToAttentionVo(Attention attention) {
    return attentionToAttentionVoMapper.convert(attention);
  }

  public NovalDetailsVo novalDetailsToNovalDetailsVo(NovalDetails novalDetails) {
    return novalDetailsToNovalDetailsVoMapper.convert(novalDetails);
  }
}
