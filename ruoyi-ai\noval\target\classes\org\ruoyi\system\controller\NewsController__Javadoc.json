{"doc": " 聊天\n\n <AUTHOR>\n @date 2025-06-17\n", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["org.ruoyi.system.domain.bo.NewsBo", "org.ruoyi.core.page.PageQuery"], "doc": " 查询消息列表\n"}, {"name": "list2", "paramTypes": ["org.ruoyi.system.domain.bo.NewsBo"], "doc": " 查询聊天列表\n"}, {"name": "export", "paramTypes": ["org.ruoyi.system.domain.bo.NewsBo", "jakarta.servlet.http.HttpServletResponse"], "doc": " 导出聊天列表\n"}, {"name": "getInfo", "paramTypes": ["java.lang.Long"], "doc": " 获取详细信息\n\n @param id 主键\n"}, {"name": "add", "paramTypes": ["org.ruoyi.system.domain.bo.NewsBo"], "doc": " 新增聊天\n"}, {"name": "edit", "paramTypes": ["org.ruoyi.system.domain.bo.NewsBo"], "doc": " 修改聊天\n"}, {"name": "edit", "paramTypes": ["java.lang.Long"], "doc": " 已读\n"}, {"name": "remove", "paramTypes": ["java.lang.Long[]"], "doc": " 删除聊天\n\n @param ids 主键串\n"}], "constructors": []}