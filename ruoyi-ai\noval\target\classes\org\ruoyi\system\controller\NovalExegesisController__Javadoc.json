{"doc": " 批注\n\n <AUTHOR>\n @date 2025-07-01\n", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["org.ruoyi.system.domain.bo.NovalExegesisBo", "org.ruoyi.core.page.PageQuery"], "doc": " 查询批注列表\n"}, {"name": "export", "paramTypes": ["org.ruoyi.system.domain.bo.NovalExegesisBo", "jakarta.servlet.http.HttpServletResponse"], "doc": " 导出批注列表\n"}, {"name": "getInfo", "paramTypes": ["java.lang.Long"], "doc": " 获取批注详细信息\n\n @param id 主键\n"}, {"name": "add", "paramTypes": ["org.ruoyi.system.domain.bo.NovalExegesisBo"], "doc": " 新增批注\n"}, {"name": "edit", "paramTypes": ["org.ruoyi.system.domain.bo.NovalExegesisBo"], "doc": " 修改批注\n"}, {"name": "remove", "paramTypes": ["java.lang.Long[]"], "doc": " 删除批注\n\n @param ids 主键串\n"}], "constructors": []}