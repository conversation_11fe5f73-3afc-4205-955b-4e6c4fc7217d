package org.ruoyi.system.domain.bo;

import java.util.LinkedHashMap;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.ruoyi.system.domain.NovalExegesis;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-12T10:45:13+0800",
    comments = "version: 1.5.3.Final, compiler: javac, environment: Java 17.0.12 (Oracle Corporation)"
)
@Component
public class NovalExegesisBoToNovalExegesisMapperImpl implements NovalExegesisBoToNovalExegesisMapper {

    @Override
    public NovalExegesis convert(NovalExegesisBo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        NovalExegesis novalExegesis = new NovalExegesis();

        novalExegesis.setSearchValue( arg0.getSearchValue() );
        novalExegesis.setCreateDept( arg0.getCreateDept() );
        novalExegesis.setCreateBy( arg0.getCreateBy() );
        novalExegesis.setCreateTime( arg0.getCreateTime() );
        novalExegesis.setUpdateBy( arg0.getUpdateBy() );
        novalExegesis.setUpdateTime( arg0.getUpdateTime() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            novalExegesis.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        novalExegesis.setId( arg0.getId() );
        novalExegesis.setUserid( arg0.getUserid() );
        novalExegesis.setNovalName( arg0.getNovalName() );
        novalExegesis.setNovalChapter( arg0.getNovalChapter() );
        novalExegesis.setNovalContent( arg0.getNovalContent() );
        novalExegesis.setExegesis( arg0.getExegesis() );
        novalExegesis.setNovalId( arg0.getNovalId() );

        return novalExegesis;
    }

    @Override
    public NovalExegesis convert(NovalExegesisBo arg0, NovalExegesis arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setSearchValue( arg0.getSearchValue() );
        arg1.setCreateDept( arg0.getCreateDept() );
        arg1.setCreateBy( arg0.getCreateBy() );
        arg1.setCreateTime( arg0.getCreateTime() );
        arg1.setUpdateBy( arg0.getUpdateBy() );
        arg1.setUpdateTime( arg0.getUpdateTime() );
        if ( arg1.getParams() != null ) {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.getParams().clear();
                arg1.getParams().putAll( map );
            }
            else {
                arg1.setParams( null );
            }
        }
        else {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.setParams( new LinkedHashMap<String, Object>( map ) );
            }
        }
        arg1.setId( arg0.getId() );
        arg1.setUserid( arg0.getUserid() );
        arg1.setNovalName( arg0.getNovalName() );
        arg1.setNovalChapter( arg0.getNovalChapter() );
        arg1.setNovalContent( arg0.getNovalContent() );
        arg1.setExegesis( arg0.getExegesis() );
        arg1.setNovalId( arg0.getNovalId() );

        return arg1;
    }
}
