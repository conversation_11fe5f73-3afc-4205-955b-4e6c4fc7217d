{"doc": " live2d\n\n <AUTHOR>\n @date 2025-06-19\n", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["org.ruoyi.system.domain.bo.Live2dBo", "org.ruoyi.core.page.PageQuery"], "doc": " 查询live2d列表\n"}, {"name": "list2", "paramTypes": ["org.ruoyi.system.domain.bo.Live2dBo"], "doc": " 根据名字/类型查询live2d列表(非表单)\n"}, {"name": "export", "paramTypes": ["org.ruoyi.system.domain.bo.Live2dBo", "jakarta.servlet.http.HttpServletResponse"], "doc": " 导出live2d列表\n"}, {"name": "getInfo", "paramTypes": ["java.lang.Long"], "doc": " 获取live2d详细信息\n\n @param id 主键\n"}, {"name": "add", "paramTypes": ["org.ruoyi.system.domain.bo.Live2dBo2"], "doc": " 新增live2d\n"}, {"name": "edit", "paramTypes": ["org.ruoyi.system.domain.bo.Live2dBo"], "doc": " 修改live2d\n"}, {"name": "remove", "paramTypes": ["java.lang.Long[]"], "doc": " 删除live2d\n\n @param ids 主键串\n"}, {"name": "remove", "paramTypes": ["java.lang.String"], "doc": " 删除live2d\n\n"}, {"name": "getByName", "paramTypes": ["java.lang.String"], "doc": " 获取live2d\n 点击应用的时候，需要传入live2dVO.name获取live2d\n"}, {"name": "getByName", "paramTypes": ["java.util.List"], "doc": " 获取icon路径\n 点击应用的时候，需要传入live2dVO.id获取live2d的icon路径\n"}], "constructors": []}