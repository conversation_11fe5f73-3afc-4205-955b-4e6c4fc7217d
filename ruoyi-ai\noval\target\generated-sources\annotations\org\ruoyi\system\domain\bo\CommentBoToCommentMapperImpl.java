package org.ruoyi.system.domain.bo;

import java.util.LinkedHashMap;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.ruoyi.system.domain.Comment;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-12T10:45:13+0800",
    comments = "version: 1.5.3.Final, compiler: javac, environment: Java 17.0.12 (Oracle Corporation)"
)
@Component
public class CommentBoToCommentMapperImpl implements CommentBoToCommentMapper {

    @Override
    public Comment convert(CommentBo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        Comment comment = new Comment();

        comment.setSearchValue( arg0.getSearchValue() );
        comment.setCreateDept( arg0.getCreateDept() );
        comment.setCreateBy( arg0.getCreateBy() );
        comment.setCreateTime( arg0.getCreateTime() );
        comment.setUpdateBy( arg0.getUpdateBy() );
        comment.setUpdateTime( arg0.getUpdateTime() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            comment.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        comment.setCommerntId( arg0.getCommerntId() );
        comment.setMyId( arg0.getMyId() );
        comment.setCommerntType( arg0.getCommerntType() );
        comment.setIsResponse( arg0.getIsResponse() );
        comment.setResponseId( arg0.getResponseId() );
        comment.setContent( arg0.getContent() );
        comment.setLook( arg0.getLook() );
        comment.setIsFine( arg0.getIsFine() );
        comment.setIcon1( arg0.getIcon1() );
        comment.setIcon2( arg0.getIcon2() );
        comment.setIcon3( arg0.getIcon3() );
        comment.setIcon4( arg0.getIcon4() );
        comment.setIcon5( arg0.getIcon5() );
        comment.setFatherid( arg0.getFatherid() );

        return comment;
    }

    @Override
    public Comment convert(CommentBo arg0, Comment arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setSearchValue( arg0.getSearchValue() );
        arg1.setCreateDept( arg0.getCreateDept() );
        arg1.setCreateBy( arg0.getCreateBy() );
        arg1.setCreateTime( arg0.getCreateTime() );
        arg1.setUpdateBy( arg0.getUpdateBy() );
        arg1.setUpdateTime( arg0.getUpdateTime() );
        if ( arg1.getParams() != null ) {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.getParams().clear();
                arg1.getParams().putAll( map );
            }
            else {
                arg1.setParams( null );
            }
        }
        else {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.setParams( new LinkedHashMap<String, Object>( map ) );
            }
        }
        arg1.setCommerntId( arg0.getCommerntId() );
        arg1.setMyId( arg0.getMyId() );
        arg1.setCommerntType( arg0.getCommerntType() );
        arg1.setIsResponse( arg0.getIsResponse() );
        arg1.setResponseId( arg0.getResponseId() );
        arg1.setContent( arg0.getContent() );
        arg1.setLook( arg0.getLook() );
        arg1.setIsFine( arg0.getIsFine() );
        arg1.setIcon1( arg0.getIcon1() );
        arg1.setIcon2( arg0.getIcon2() );
        arg1.setIcon3( arg0.getIcon3() );
        arg1.setIcon4( arg0.getIcon4() );
        arg1.setIcon5( arg0.getIcon5() );
        arg1.setFatherid( arg0.getFatherid() );

        return arg1;
    }
}
