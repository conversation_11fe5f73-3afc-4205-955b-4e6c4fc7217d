package org.ruoyi.system.domain.bo;

import java.util.LinkedHashMap;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.ruoyi.system.domain.CommentGoodbad;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-12T10:45:13+0800",
    comments = "version: 1.5.3.Final, compiler: javac, environment: Java 17.0.12 (Oracle Corporation)"
)
@Component
public class CommentGoodbadBoToCommentGoodbadMapperImpl implements CommentGoodbadBoToCommentGoodbadMapper {

    @Override
    public CommentGoodbad convert(CommentGoodbadBo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        CommentGoodbad commentGoodbad = new CommentGoodbad();

        commentGoodbad.setSearchValue( arg0.getSearchValue() );
        commentGoodbad.setCreateDept( arg0.getCreateDept() );
        commentGoodbad.setCreateBy( arg0.getCreateBy() );
        commentGoodbad.setCreateTime( arg0.getCreateTime() );
        commentGoodbad.setUpdateBy( arg0.getUpdateBy() );
        commentGoodbad.setUpdateTime( arg0.getUpdateTime() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            commentGoodbad.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        commentGoodbad.setCommerntId( arg0.getCommerntId() );
        commentGoodbad.setWhoId( arg0.getWhoId() );
        commentGoodbad.setGoodBad( arg0.getGoodBad() );

        return commentGoodbad;
    }

    @Override
    public CommentGoodbad convert(CommentGoodbadBo arg0, CommentGoodbad arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setSearchValue( arg0.getSearchValue() );
        arg1.setCreateDept( arg0.getCreateDept() );
        arg1.setCreateBy( arg0.getCreateBy() );
        arg1.setCreateTime( arg0.getCreateTime() );
        arg1.setUpdateBy( arg0.getUpdateBy() );
        arg1.setUpdateTime( arg0.getUpdateTime() );
        if ( arg1.getParams() != null ) {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.getParams().clear();
                arg1.getParams().putAll( map );
            }
            else {
                arg1.setParams( null );
            }
        }
        else {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.setParams( new LinkedHashMap<String, Object>( map ) );
            }
        }
        arg1.setCommerntId( arg0.getCommerntId() );
        arg1.setWhoId( arg0.getWhoId() );
        arg1.setGoodBad( arg0.getGoodBad() );

        return arg1;
    }
}
