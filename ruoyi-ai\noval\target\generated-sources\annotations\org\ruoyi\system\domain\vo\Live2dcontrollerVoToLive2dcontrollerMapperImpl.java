package org.ruoyi.system.domain.vo;

import javax.annotation.processing.Generated;
import org.ruoyi.system.domain.Live2dcontroller;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-12T10:45:13+0800",
    comments = "version: 1.5.3.Final, compiler: javac, environment: Java 17.0.12 (Oracle Corporation)"
)
@Component
public class Live2dcontrollerVoToLive2dcontrollerMapperImpl implements Live2dcontrollerVoToLive2dcontrollerMapper {

    @Override
    public Live2dcontroller convert(Live2dcontrollerVo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        Live2dcontroller live2dcontroller = new Live2dcontroller();

        live2dcontroller.setId( arg0.getId() );
        live2dcontroller.setUserid( arg0.getUserid() );
        live2dcontroller.setName( arg0.getName() );

        return live2dcontroller;
    }

    @Override
    public Live2dcontroller convert(Live2dcontrollerVo arg0, Live2dcontroller arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setUserid( arg0.getUserid() );
        arg1.setName( arg0.getName() );

        return arg1;
    }
}
