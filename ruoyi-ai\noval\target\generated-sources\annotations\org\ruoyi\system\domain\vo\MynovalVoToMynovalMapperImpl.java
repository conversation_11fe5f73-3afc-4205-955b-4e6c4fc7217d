package org.ruoyi.system.domain.vo;

import javax.annotation.processing.Generated;
import org.ruoyi.system.domain.Mynoval;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-12T10:45:13+0800",
    comments = "version: 1.5.3.Final, compiler: javac, environment: Java 17.0.12 (Oracle Corporation)"
)
@Component
public class MynovalVoToMynovalMapperImpl implements MynovalVoToMynovalMapper {

    @Override
    public Mynoval convert(MynovalVo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        Mynoval mynoval = new Mynoval();

        mynoval.setId( arg0.getId() );
        mynoval.setUserid( arg0.getUserid() );
        mynoval.setCosid( arg0.getCosid() );

        return mynoval;
    }

    @Override
    public Mynoval convert(MynovalVo arg0, Mynoval arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setUserid( arg0.getUserid() );
        arg1.setCosid( arg0.getCosid() );

        return arg1;
    }
}
