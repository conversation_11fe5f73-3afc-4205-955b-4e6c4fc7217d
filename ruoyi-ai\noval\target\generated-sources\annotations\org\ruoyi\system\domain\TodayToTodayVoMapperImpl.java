package org.ruoyi.system.domain;

import javax.annotation.processing.Generated;
import org.ruoyi.system.domain.vo.TodayVo;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-12T10:45:13+0800",
    comments = "version: 1.5.3.Final, compiler: javac, environment: Java 17.0.12 (Oracle Corporation)"
)
@Component
public class TodayToTodayVoMapperImpl implements TodayToTodayVoMapper {

    @Override
    public TodayVo convert(Today arg0) {
        if ( arg0 == null ) {
            return null;
        }

        TodayVo todayVo = new TodayVo();

        todayVo.setNewCommernt( arg0.getNewCommernt() );
        todayVo.setId( arg0.getId() );

        return todayVo;
    }

    @Override
    public TodayVo convert(Today arg0, TodayVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setNewCommernt( arg0.getNewCommernt() );
        arg1.setId( arg0.getId() );

        return arg1;
    }
}
