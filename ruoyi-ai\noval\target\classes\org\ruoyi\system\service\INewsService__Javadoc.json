{"doc": " 聊天Service接口\n\n <AUTHOR>\n @date 2025-06-17\n", "fields": [], "enumConstants": [], "methods": [{"name": "queryById", "paramTypes": ["java.lang.Long"], "doc": " 查询聊天\n"}, {"name": "queryPageList", "paramTypes": ["org.ruoyi.system.domain.bo.NewsBo", "org.ruoyi.core.page.PageQuery"], "doc": " 查询聊天列表\n"}, {"name": "queryList", "paramTypes": ["org.ruoyi.system.domain.bo.NewsBo"], "doc": " 查询聊天列表\n"}, {"name": "insertByBo", "paramTypes": ["org.ruoyi.system.domain.bo.NewsBo"], "doc": " 新增聊天\n"}, {"name": "updateByBo", "paramTypes": ["org.ruoyi.system.domain.bo.NewsBo"], "doc": " 修改聊天\n"}, {"name": "deleteWithValidByIds", "paramTypes": ["java.util.Collection", "java.lang.Bo<PERSON>an"], "doc": " 校验并批量删除聊天信息\n"}], "constructors": []}