package org.ruoyi.system.domain;

import javax.annotation.processing.Generated;
import org.ruoyi.system.domain.vo.NovalExegesisVo;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-12T10:45:13+0800",
    comments = "version: 1.5.3.Final, compiler: javac, environment: Java 17.0.12 (Oracle Corporation)"
)
@Component
public class NovalExegesisToNovalExegesisVoMapperImpl implements NovalExegesisToNovalExegesisVoMapper {

    @Override
    public NovalExegesisVo convert(NovalExegesis arg0) {
        if ( arg0 == null ) {
            return null;
        }

        NovalExegesisVo novalExegesisVo = new NovalExegesisVo();

        novalExegesisVo.setId( arg0.getId() );
        novalExegesisVo.setUserid( arg0.getUserid() );
        novalExegesisVo.setNovalName( arg0.getNovalName() );
        novalExegesisVo.setNovalChapter( arg0.getNovalChapter() );
        novalExegesisVo.setNovalContent( arg0.getNovalContent() );
        novalExegesisVo.setExegesis( arg0.getExegesis() );
        novalExegesisVo.setNovalId( arg0.getNovalId() );

        return novalExegesisVo;
    }

    @Override
    public NovalExegesisVo convert(NovalExegesis arg0, NovalExegesisVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setUserid( arg0.getUserid() );
        arg1.setNovalName( arg0.getNovalName() );
        arg1.setNovalChapter( arg0.getNovalChapter() );
        arg1.setNovalContent( arg0.getNovalContent() );
        arg1.setExegesis( arg0.getExegesis() );
        arg1.setNovalId( arg0.getNovalId() );

        return arg1;
    }
}
