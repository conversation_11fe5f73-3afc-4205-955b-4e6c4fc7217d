{"doc": " 今日newService接口\n\n <AUTHOR>\n @date 2025-06-16\n", "fields": [], "enumConstants": [], "methods": [{"name": "queryPageList", "paramTypes": ["org.ruoyi.system.domain.bo.TodayBo", "org.ruoyi.core.page.PageQuery"], "doc": " 查询今日new列表\n"}, {"name": "queryList", "paramTypes": ["org.ruoyi.system.domain.bo.TodayBo"], "doc": " 查询今日new列表\n"}, {"name": "insertByBo", "paramTypes": ["org.ruoyi.system.domain.bo.TodayBo"], "doc": " 新增今日new\n"}, {"name": "updateByBo", "paramTypes": ["org.ruoyi.system.domain.bo.TodayBo"], "doc": " 修改今日new\n"}], "constructors": []}