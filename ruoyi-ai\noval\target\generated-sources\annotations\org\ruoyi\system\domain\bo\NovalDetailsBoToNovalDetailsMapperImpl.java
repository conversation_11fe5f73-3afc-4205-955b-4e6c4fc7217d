package org.ruoyi.system.domain.bo;

import java.util.LinkedHashMap;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.ruoyi.system.domain.NovalDetails;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-12T10:45:13+0800",
    comments = "version: 1.5.3.Final, compiler: javac, environment: Java 17.0.12 (Oracle Corporation)"
)
@Component
public class NovalDetailsBoToNovalDetailsMapperImpl implements NovalDetailsBoToNovalDetailsMapper {

    @Override
    public NovalDetails convert(NovalDetailsBo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        NovalDetails novalDetails = new NovalDetails();

        novalDetails.setSearchValue( arg0.getSearchValue() );
        novalDetails.setCreateDept( arg0.getCreateDept() );
        novalDetails.setCreateBy( arg0.getCreateBy() );
        novalDetails.setCreateTime( arg0.getCreateTime() );
        novalDetails.setUpdateBy( arg0.getUpdateBy() );
        novalDetails.setUpdateTime( arg0.getUpdateTime() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            novalDetails.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        novalDetails.setId( arg0.getId() );
        novalDetails.setGrades( arg0.getGrades() );
        novalDetails.setType( arg0.getType() );
        novalDetails.setPlatform( arg0.getPlatform() );
        novalDetails.setSummary( arg0.getSummary() );
        novalDetails.setIcon( arg0.getIcon() );
        novalDetails.setLook( arg0.getLook() );

        return novalDetails;
    }

    @Override
    public NovalDetails convert(NovalDetailsBo arg0, NovalDetails arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setSearchValue( arg0.getSearchValue() );
        arg1.setCreateDept( arg0.getCreateDept() );
        arg1.setCreateBy( arg0.getCreateBy() );
        arg1.setCreateTime( arg0.getCreateTime() );
        arg1.setUpdateBy( arg0.getUpdateBy() );
        arg1.setUpdateTime( arg0.getUpdateTime() );
        if ( arg1.getParams() != null ) {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.getParams().clear();
                arg1.getParams().putAll( map );
            }
            else {
                arg1.setParams( null );
            }
        }
        else {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.setParams( new LinkedHashMap<String, Object>( map ) );
            }
        }
        arg1.setId( arg0.getId() );
        arg1.setGrades( arg0.getGrades() );
        arg1.setType( arg0.getType() );
        arg1.setPlatform( arg0.getPlatform() );
        arg1.setSummary( arg0.getSummary() );
        arg1.setIcon( arg0.getIcon() );
        arg1.setLook( arg0.getLook() );

        return arg1;
    }
}
