{"doc": " 用户浏览书库Service业务层处理\n\n <AUTHOR>\n @date 2025-06-11\n", "fields": [], "enumConstants": [], "methods": [{"name": "afterPropertiesSet", "paramTypes": [], "doc": " Spring Bean初始化完成后自动执行\n"}, {"name": "creteIndex", "paramTypes": ["co.elastic.clients.elasticsearch.ElasticsearchClient"], "doc": " 创建索引（优化单节点ES配置）\n"}, {"name": "initializeBookIds", "paramTypes": [], "doc": " 初始化所有有效书籍ID到布谷鸟过滤器\n"}, {"name": "addDefaultFilterValues", "paramTypes": [], "doc": " 添加默认的过滤器值，确保常用参数始终可用\n"}, {"name": "queryPageList", "paramTypes": ["org.ruoyi.system.domain.bo.NovalForYhBo", "org.ruoyi.core.page.PageQuery"], "doc": " 查询用户浏览书库列表\n"}, {"name": "loadData", "paramTypes": ["org.ruoyi.system.domain.bo.NovalForYhBo", "org.ruoyi.core.page.PageQuery"], "doc": " 查询用户浏览书库列表\n"}, {"name": "loadFromDatabase", "paramTypes": ["java.lang.Long"], "doc": " 从数据库直接加载数据\n"}], "constructors": []}