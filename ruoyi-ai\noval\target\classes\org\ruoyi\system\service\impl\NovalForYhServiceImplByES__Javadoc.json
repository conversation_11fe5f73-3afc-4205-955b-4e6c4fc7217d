{"doc": " 用户浏览书库Service业务层处理\n\n <AUTHOR>\n @date 2025-06-11\n", "fields": [], "enumConstants": [], "methods": [{"name": "afterPropertiesSet", "paramTypes": [], "doc": " Spring Bean初始化完成后自动执行\n"}, {"name": "creteIndex", "paramTypes": ["co.elastic.clients.elasticsearch.ElasticsearchClient"], "doc": " 创建索引（优化单节点ES配置）\n"}, {"name": "initializeBookIds", "paramTypes": [], "doc": " 初始化所有有效书籍ID到布谷鸟过滤器\n"}, {"name": "addDefaultFilterValues", "paramTypes": [], "doc": " 添加默认的过滤器值，确保常用参数始终可用\n"}, {"name": "ensureIndexExists", "paramTypes": ["co.elastic.clients.elasticsearch.ElasticsearchClient", "org.ruoyi.system.domain.bo.NovalForYhBo", "org.ruoyi.core.page.PageQuery"], "doc": " 确保ES索引存在，处理索引检查和创建的异常\n @param client ES客户端\n @param bo 查询条件\n @param pageQuery 分页参数\n @return 索引是否准备就绪\n"}, {"name": "queryPageList", "paramTypes": ["org.ruoyi.system.domain.bo.NovalForYhBo", "org.ruoyi.core.page.PageQuery"], "doc": " 查询用户浏览书库列表\n"}, {"name": "loadData", "paramTypes": ["org.ruoyi.system.domain.bo.NovalForYhBo", "org.ruoyi.core.page.PageQuery"], "doc": " 查询用户浏览书库列表\n"}, {"name": "loadFromDatabase", "paramTypes": ["java.lang.Long"], "doc": " 从数据库直接加载数据\n"}, {"name": "optimizeSuggestions", "paramTypes": ["java.util.List", "java.lang.String"], "doc": " 优化自动补全建议结果\n @param suggestions 原始建议列表\n @param keyword 用户输入的关键词\n @return 优化后的建议列表\n"}, {"name": "getDatabaseSuggestions", "paramTypes": ["java.lang.String"], "doc": " 数据库降级查询自动补全建议\n @param keyword 用户输入的关键词\n @return 建议列表\n"}], "constructors": []}