package org.ruoyi.system.domain.vo;

import lombok.NoArgsConstructor;
import org.ruoyi.common.excel.annotation.ExcelDictFormat;
import org.ruoyi.common.excel.convert.ExcelDictConvert;
import org.ruoyi.system.domain.Noval;
import org.ruoyi.system.domain.NovalForYh;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;



/**
 * 用户浏览书库视图对象 noval_for_yh
 *
 * <AUTHOR>
 * @date 2025-06-11
 */
@Data
@ExcelIgnoreUnannotated
@NoArgsConstructor
@AutoMapper(target = NovalForYh.class)
public class NovalForYhVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 小说id
     */
    @ExcelProperty(value = "小说id")
    private Long id;

    /**
     * 小说名称
     */
    @ExcelProperty(value = "小说名称")
    private String name;

    /**
     * 小说作者
     */
    @ExcelProperty(value = "小说作者")
    private String author;

    /**
     * 小说状态
     */
    @ExcelProperty(value = "小说状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "sys_noval_state")
    private String flag;

    /**
     * 小说平台
     */
    @ExcelProperty(value = "小说平台")
    private String platform;

    /**
     * 小说成绩
     */
    @ExcelProperty(value = "小说成绩", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "sys_noval_platform")
    private Long grades;

    /**
     * 小说类型
     */
    @ExcelProperty(value = "小说类型", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "sys_noval_type")
    private String type;

    /**
     * 小说简介
     */
    @ExcelProperty(value = "小说简介")
    private String summary;

    /**
     * 小说图标
     */
    @ExcelProperty(value = "小说图标")
    private String icon;

    /**
     * 点击次数
     */
    @ExcelProperty(value = "点击次数")
    private String look;

    // ========== ES自动补全字段 ==========

    /**
     * 书名自动补全字段
     */
    private String nameSuggest;

    /**
     * 作者自动补全字段
     */
    private String authorSuggest;

    /**
     * 类型自动补全字段
     */
    private String typeSuggest;

    /**
     * 综合自动补全字段（书名+作者+类型）
     */
    private String allSuggest;


    public NovalForYhVo(NovalVo novalVo, NovalDetailsVo novalDetailsVo) {
        this.id = novalDetailsVo.getId();
        this.name = novalVo.getName();
        this.author = novalVo.getAuthor();
        this.flag = novalVo.getFlag();
        this.grades = novalDetailsVo.getGrades();
        this.platform = novalDetailsVo.getPlatform();
        this.type = novalDetailsVo.getType();
        this.icon = novalDetailsVo.getIcon();
        this.summary= novalDetailsVo.getSummary();
        this.look = novalDetailsVo.getLook();

        // 初始化自动补全字段
        initSuggestFields();
    }

    public NovalForYhVo(Noval noval) {
        this.id = noval.getId();
        this.name = noval.getName();
        this.author = noval.getAuthor();
        this.flag = noval.getFlag();
        this.grades = 0L;
        this.platform = "";
        this.type = "";
        this.icon = "";
        this.summary= "";
        this.look = "0";

        // 初始化自动补全字段
        initSuggestFields();
    }

    /**
     * 初始化自动补全字段
     */
    private void initSuggestFields() {
        // 书名自动补全（去除空格和特殊字符）
        this.nameSuggest = this.name != null ? this.name.trim() : "";

        // 作者自动补全
        this.authorSuggest = this.author != null ? this.author.trim() : "";

        // 类型自动补全
        this.typeSuggest = this.type != null ? this.type.trim() : "";

        // 综合自动补全（书名 + 作者 + 类型）
        StringBuilder allSuggestBuilder = new StringBuilder();
        if (this.name != null && !this.name.trim().isEmpty()) {
            allSuggestBuilder.append(this.name.trim());
        }
        if (this.author != null && !this.author.trim().isEmpty()) {
            if (allSuggestBuilder.length() > 0) allSuggestBuilder.append(" ");
            allSuggestBuilder.append(this.author.trim());
        }
        if (this.type != null && !this.type.trim().isEmpty()) {
            if (allSuggestBuilder.length() > 0) allSuggestBuilder.append(" ");
            allSuggestBuilder.append(this.type.trim());
        }
        this.allSuggest = allSuggestBuilder.toString();
    }
}
