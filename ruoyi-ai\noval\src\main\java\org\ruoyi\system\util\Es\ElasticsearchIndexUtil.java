package org.ruoyi.system.util.Es;

import co.elastic.clients.elasticsearch.ElasticsearchClient;
import co.elastic.clients.elasticsearch._types.Result;
import co.elastic.clients.elasticsearch._types.query_dsl.BoolQuery;
import co.elastic.clients.elasticsearch.core.*;
import co.elastic.clients.elasticsearch.core.search.*;
import co.elastic.clients.elasticsearch.indices.*;
import co.elastic.clients.elasticsearch._types.mapping.Property;
import co.elastic.clients.elasticsearch.indices.ExistsRequest;
import co.elastic.clients.elasticsearch.indices.analyze.AnalyzeToken; // 添加缺失的导入
import co.elastic.clients.json.jackson.JacksonJsonpMapper;
import co.elastic.clients.transport.ElasticsearchTransport;
import co.elastic.clients.transport.rest_client.RestClientTransport;
import org.apache.http.HttpHost;
import org.elasticsearch.client.RestClient;

import java.io.IOException;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;


public class ElasticsearchIndexUtil {
    private static ElasticsearchClient currentClient;
    private static RestClient restClient;

    // 私有构造方法防止实例化
    private ElasticsearchIndexUtil() {
        throw new UnsupportedOperationException("工具类禁止实例化");
    }

    /**
     * 创建/获取Elasticsearch客户端 (单例模式)
     */
    public static synchronized ElasticsearchClient createClient() {
        if (currentClient == null || !isClientActive(currentClient)) {
            closeCurrentClient(); // 关闭旧连接
            restClient = RestClient.builder(
                    new HttpHost("***************", 9200)
            ).build();

            ElasticsearchTransport transport = new RestClientTransport(
                    restClient, new JacksonJsonpMapper()
            );
            currentClient = new ElasticsearchClient(transport);
        }
        return currentClient;
    }

    /**
     * 检查客户端是否活跃
     */
    private static boolean isClientActive(ElasticsearchClient client) {
        try {
            client.ping();
            return true;
        } catch (IOException e) {
            return false;
        }
    }

    /**
     * 关闭当前客户端
     */
    public static synchronized void closeCurrentClient() {
        try {
            if (currentClient != null) {
                currentClient._transport().close();
                currentClient = null;
            }
            if (restClient != null) {
                restClient.close();
                restClient = null;
            }
        } catch (IOException e) {
            System.err.println("关闭ES客户端时出错: " + e.getMessage());
        }
    }

    /**
     * 创建索引（优化单节点配置）
     */
    public static boolean createIndex(ElasticsearchClient client,
                                      String indexName,
                                      Map<String, Property> properties) throws IOException {
        if (indexExists(client, indexName)) {
            return true;
        }

        // 单节点ES优化配置：1个分片，0个副本
        CreateIndexRequest request = CreateIndexRequest.of(b -> b
                .index(indexName)
                .mappings(m -> m.properties(properties))
                .settings(s -> s
                    .numberOfShards("1")      // 单分片
                    .numberOfReplicas("0")    // 无副本（单节点环境）
                    .refreshInterval(t -> t.time("1s"))    // 快速刷新
                )
        );

        CreateIndexResponse response = client.indices().create(request);
        return response.acknowledged();
    }

    /**
     * 检查索引是否存在
     */
    public static boolean indexExists(ElasticsearchClient client, String indexName) throws IOException {
        return client.indices().exists(ExistsRequest.of(e -> e.index(indexName))).value();
    }

    /**
     * 检查文档是否存在
     */
    public static boolean docExists(ElasticsearchClient client,
                                    String indexName,
                                    String documentId) throws IOException {
        return client.exists(e -> e.index(indexName).id(documentId)).value();
    }
    /**
     * 删除索引
     */
    public static boolean deleteIndex(ElasticsearchClient client, String indexName) throws IOException {
        DeleteIndexRequest request = DeleteIndexRequest.of(d -> d.index(indexName));
        return client.indices().delete(request).acknowledged();
    }

    // ==================== 文档操作方法 ====================

    /**
     * 添加文档（自动生成ID）
     *
     * @param <T> 文档类型
     * @return 生成的文档ID
     */
    public static <T> String addDocument(ElasticsearchClient client,
                                         String indexName,
                                         T document) throws IOException {
        indexExists(client, indexName); // 检查索引是否存在
        IndexResponse response = client.index(i -> i
                .index(indexName)
                .document(document));
        return response.id();
    }

    /**
     * 添加或更新文档（指定ID）
     *
     * @param <T> 文档类型
     * @return 操作结果（CREATED 或 UPDATED）
     */
    public static <T> Result upsertDocument(ElasticsearchClient client,
                                            String indexName,
                                            String documentId,
                                            T document) throws IOException {
        IndexResponse response = client.index(i -> i
                .index(indexName)
                .id(documentId)
                .document(document));
        return response.result();
    }

    /**
     * 更新文档（部分字段）
     *
     * @return 操作结果（UPDATED 或 NOOP）
     */
    public static Result updateDocument(ElasticsearchClient client,
                                        String indexName,
                                        String documentId,
                                        Object updateContent) throws IOException {
        // 自动识别输入类型处理
        if (updateContent instanceof Map) {
            // Map类型使用部分更新
            UpdateResponse<?> response = client.update(u -> u
                    .index(indexName)
                    .id(documentId)
                    .doc(updateContent), Object.class);
            return response.result();
        } else {
            // 非Map类型使用全量替换（upsert语义）
            IndexResponse response = client.index(i -> i
                    .index(indexName)
                    .id(documentId)
                    .document(updateContent));
            return response.result();
        }
    }

    /**
     * 删除文档
     *
     * @return 操作结果（DELETED 或 NOT_FOUND）
     */
    public static Result deleteDocument(ElasticsearchClient client,
                                        String indexName,
                                        String documentId) throws IOException {
        DeleteResponse response = client.delete(d -> d
                .index(indexName)
                .id(documentId));
        return response.result();
    }

    /**
     * 获取文档
     *
     * @param <T> 文档类型
     * @return 文档对象（未找到返回null）
     */
    public static <T> T getDocument(ElasticsearchClient client,
                                    String indexName,
                                    String documentId,
                                    Class<T> documentType) throws IOException {
        GetResponse<T> response = client.get(g -> g
                .index(indexName)
                .id(documentId), documentType);
        return response.found() ? response.source() : null;
    }

    /**
     * 批量添加文档
     *
     * @param <T> 文档类型
     * @return 批量操作结果
     */
    public static <T> BulkResponse bulkAddDocuments(ElasticsearchClient client,
                                                    String indexName,
                                                    Map<String, T> documents) throws IOException {
        // 如果 documents 为空，直接返回空的 BulkResponse
        if (documents == null || documents.isEmpty()) {
            return new BulkResponse.Builder().errors(false).took(0).build();
        }

        BulkRequest.Builder builder = new BulkRequest.Builder();

        for (Map.Entry<String, T> entry : documents.entrySet()) {
            builder.operations(op -> op
                    .index(idx -> idx
                            .index(indexName)
                            .id(entry.getKey())
                            .document(entry.getValue())
                    )
            );
        }
        return client.bulk(builder.build());
    }

    /**
     * 搜索文档
     *
     * @param <T> 文档类型
     * @return 匹配的文档列表
     */
    public static <T> List<T> searchDocuments(ElasticsearchClient client,
                                              String indexName,
                                              String field,
                                              String value,
                                              Class<T> documentType) throws IOException {
        SearchResponse<T> response = client.search(s -> s
                .index(indexName)
                .query(q -> q
                        .match(m -> m
                                .field(field)
                                .query(value)
                        )
                ), documentType);

        return response.hits().hits().stream()
                .map(Hit::source)
                .collect(Collectors.toList());
    }

    /**
     * 关闭客户端连接
     */
    public static void closeClient(ElasticsearchClient client) throws IOException {
        if (client != null) {
            client._transport().close();
        }
    }


    /**
     * 文本分词
     *
     * @param client Elasticsearch客户端
     * @param index 索引名称（可为空）
     * @param text 待分词文本
     * @param analyzer 分词器名称
     * @return 分词结果列表
     * @throws IOException
     */
    public static List<String> analyze(ElasticsearchClient client,
                                       String index,
                                       String text,
                                       String analyzer) throws IOException {
        AnalyzeRequest request = AnalyzeRequest.of(a -> a
                .index(index)
                .analyzer(analyzer)
                .text(text)
        );

        AnalyzeResponse response = client.indices().analyze(request);
        return response.tokens().stream()
                .map(AnalyzeToken::token)
                .collect(Collectors.toList());
    }


    /**
     * 自动补全建议
     *
     * @param client Elasticsearch客户端
     * @param index 索引名称
     * @param keyword 用户输入的关键词
     * @param fieldName 补全字段名称（默认为"tags"）
     * @return 补全建议列表
     * @throws IOException
     */
    public static List<String> autoSuggest(ElasticsearchClient client,
                                           String index,
                                           String keyword,
                                           String fieldName) throws IOException {
        // 1. 创建自动补全请求
        Suggester suggester = Suggester.of(s -> s
                .suggesters("completion_suggestion", fs -> fs
                        .completion(cs -> cs
                                .skipDuplicates(true)
                                .size(10)
                                .field(fieldName)
                        )
                )
                .text(keyword)
        );

        // 2. 执行自动补全查询
        SearchResponse<Void> response = client.search(s -> s
                .index(index)
                .suggest(suggester), Void.class);

        // 3. 处理补全结果
        return response.suggest().get("completion_suggestion").stream()
                .flatMap(suggestion -> suggestion.completion().options().stream())
                .map(CompletionSuggestOption::text)
                .collect(Collectors.toList());
    }

    /**
     * 多字段自动补全建议（增强版）
     *
     * @param client Elasticsearch客户端
     * @param index 索引名称
     * @param keyword 用户输入的关键词
     * @param fieldNames 多个补全字段名称
     * @param maxSize 最大返回数量
     * @return 补全建议列表（去重排序）
     * @throws IOException
     */
    public static List<String> multiFieldAutoSuggest(ElasticsearchClient client,
                                                     String index,
                                                     String keyword,
                                                     String[] fieldNames,
                                                     int maxSize) throws IOException {
        // 1. 创建多字段自动补全请求
        Suggester.Builder suggesterBuilder = new Suggester.Builder();

        // 为每个字段创建补全建议
        for (int i = 0; i < fieldNames.length; i++) {
            String fieldName = fieldNames[i];
            String suggesterName = "suggestion_" + i;

            suggesterBuilder.suggesters(suggesterName, fs -> fs
                    .completion(cs -> cs
                            .skipDuplicates(true)
                            .size(maxSize)
                            .field(fieldName)
                    )
            );
        }

        suggesterBuilder.text(keyword);

        // 2. 执行自动补全查询
        SearchResponse<Void> response = client.search(s -> s
                .index(index)
                .suggest(suggesterBuilder.build()), Void.class);

        // 3. 处理多字段补全结果，去重并排序
        Set<String> uniqueSuggestions = new LinkedHashSet<>();

        for (int i = 0; i < fieldNames.length; i++) {
            String suggesterName = "suggestion_" + i;
            if (response.suggest().containsKey(suggesterName)) {
                response.suggest().get(suggesterName).stream()
                        .flatMap(suggestion -> suggestion.completion().options().stream())
                        .map(CompletionSuggestOption::text)
                        .filter(text -> text != null && !text.trim().isEmpty())
                        .forEach(uniqueSuggestions::add);
            }
        }

        // 4. 转换为列表并限制数量
        return uniqueSuggestions.stream()
                .limit(maxSize)
                .collect(Collectors.toList());
    }

    /**
     * 多条件AND搜索文档
     *
     * @param <T> 文档类型
     * @param conditions 查询条件Map(key=字段名, value=字段值)
     * @return 匹配的文档列表
     */
    public static <T> List<T> searchDocuments(ElasticsearchClient client,
                                              String indexName,
                                              Map<String, String> conditions,
                                              Class<T> documentType) throws IOException {
        BoolQuery.Builder boolBuilder = new BoolQuery.Builder();

        conditions.forEach((field, value) ->
                boolBuilder.must(m -> m
                        .match(t -> t
                                .field(field)
                                .query(value)
                        )
                )
        );

        SearchResponse<T> response = client.search(s -> s
                        .index(indexName)
                        .query(q -> q.bool(boolBuilder.build())),
                documentType);

        return response.hits().hits().stream()
                .map(Hit::source)
                .collect(Collectors.toList());
    }

    /**
     * 多条件OR搜索文档
     *
     * @param <T> 文档类型
     * @param conditions 查询条件Map(key=字段名, value=字段值)
     * @return 匹配的文档列表
     */
    public static <T> List<T> searchDocumentsOr(ElasticsearchClient client,
                                                String indexName,
                                                Map<String, String> conditions,
                                                Class<T> documentType) throws IOException {
        BoolQuery.Builder boolBuilder = new BoolQuery.Builder();

        conditions.forEach((field, value) ->
                boolBuilder.should(m -> m
                        .match(t -> t
                                .field(field)
                                .query(value)
                        )
                )
        );

        SearchResponse<T> response = client.search(s -> s
                        .index(indexName)
                        .query(q -> q.bool(boolBuilder.minimumShouldMatch("1").build())),
                documentType);

        return response.hits().hits().stream()
                .map(Hit::source)
                .collect(Collectors.toList());
    }
}