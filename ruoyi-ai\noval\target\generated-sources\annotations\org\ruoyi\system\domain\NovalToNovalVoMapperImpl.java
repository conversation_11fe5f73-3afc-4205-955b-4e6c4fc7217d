package org.ruoyi.system.domain;

import javax.annotation.processing.Generated;
import org.ruoyi.system.domain.vo.NovalVo;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-12T10:45:13+0800",
    comments = "version: 1.5.3.Final, compiler: javac, environment: Java 17.0.12 (Oracle Corporation)"
)
@Component
public class NovalToNovalVoMapperImpl implements NovalToNovalVoMapper {

    @Override
    public NovalVo convert(Noval arg0) {
        if ( arg0 == null ) {
            return null;
        }

        NovalVo novalVo = new NovalVo();

        novalVo.setId( arg0.getId() );
        novalVo.setName( arg0.getName() );
        novalVo.setAuthor( arg0.getAuthor() );
        novalVo.setFlag( arg0.getFlag() );
        novalVo.setCreateTime( arg0.getCreateTime() );

        return novalVo;
    }

    @Override
    public NovalVo convert(Noval arg0, NovalVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setName( arg0.getName() );
        arg1.setAuthor( arg0.getAuthor() );
        arg1.setFlag( arg0.getFlag() );
        arg1.setCreateTime( arg0.getCreateTime() );

        return arg1;
    }
}
