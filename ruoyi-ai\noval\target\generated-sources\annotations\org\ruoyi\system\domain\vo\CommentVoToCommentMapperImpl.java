package org.ruoyi.system.domain.vo;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import javax.annotation.processing.Generated;
import org.ruoyi.system.domain.Comment;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-12T10:45:13+0800",
    comments = "version: 1.5.3.Final, compiler: javac, environment: Java 17.0.12 (Oracle Corporation)"
)
@Component
public class CommentVoToCommentMapperImpl implements CommentVoToCommentMapper {

    @Override
    public Comment convert(CommentVo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        Comment comment = new Comment();

        try {
            if ( arg0.getCreateTime() != null ) {
                comment.setCreateTime( new SimpleDateFormat().parse( arg0.getCreateTime() ) );
            }
        }
        catch ( ParseException e ) {
            throw new RuntimeException( e );
        }
        comment.setCommerntId( arg0.getCommerntId() );
        comment.setMyId( arg0.getMyId() );
        comment.setCommerntType( arg0.getCommerntType() );
        comment.setIsResponse( arg0.getIsResponse() );
        comment.setResponseId( arg0.getResponseId() );
        comment.setContent( arg0.getContent() );
        comment.setLook( arg0.getLook() );
        comment.setIsFine( arg0.getIsFine() );
        comment.setIcon1( arg0.getIcon1() );
        comment.setIcon2( arg0.getIcon2() );
        comment.setIcon3( arg0.getIcon3() );
        comment.setIcon4( arg0.getIcon4() );
        comment.setIcon5( arg0.getIcon5() );
        comment.setFatherid( arg0.getFatherid() );

        return comment;
    }

    @Override
    public Comment convert(CommentVo arg0, Comment arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        try {
            if ( arg0.getCreateTime() != null ) {
                arg1.setCreateTime( new SimpleDateFormat().parse( arg0.getCreateTime() ) );
            }
            else {
                arg1.setCreateTime( null );
            }
        }
        catch ( ParseException e ) {
            throw new RuntimeException( e );
        }
        arg1.setCommerntId( arg0.getCommerntId() );
        arg1.setMyId( arg0.getMyId() );
        arg1.setCommerntType( arg0.getCommerntType() );
        arg1.setIsResponse( arg0.getIsResponse() );
        arg1.setResponseId( arg0.getResponseId() );
        arg1.setContent( arg0.getContent() );
        arg1.setLook( arg0.getLook() );
        arg1.setIsFine( arg0.getIsFine() );
        arg1.setIcon1( arg0.getIcon1() );
        arg1.setIcon2( arg0.getIcon2() );
        arg1.setIcon3( arg0.getIcon3() );
        arg1.setIcon4( arg0.getIcon4() );
        arg1.setIcon5( arg0.getIcon5() );
        arg1.setFatherid( arg0.getFatherid() );

        return arg1;
    }
}
