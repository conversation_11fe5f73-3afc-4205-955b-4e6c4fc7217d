package org.ruoyi.system.domain.bo;

import java.util.LinkedHashMap;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.ruoyi.system.domain.Today;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-12T10:45:13+0800",
    comments = "version: 1.5.3.Final, compiler: javac, environment: Java 17.0.12 (Oracle Corporation)"
)
@Component
public class TodayBoToTodayMapperImpl implements TodayBoToTodayMapper {

    @Override
    public Today convert(TodayBo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        Today today = new Today();

        today.setSearchValue( arg0.getSearchValue() );
        today.setCreateDept( arg0.getCreateDept() );
        today.setCreateBy( arg0.getCreateBy() );
        today.setCreateTime( arg0.getCreateTime() );
        today.setUpdateBy( arg0.getUpdateBy() );
        today.setUpdateTime( arg0.getUpdateTime() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            today.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        today.setId( arg0.getId() );
        today.setNewCommernt( arg0.getNewCommernt() );

        return today;
    }

    @Override
    public Today convert(TodayBo arg0, Today arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setSearchValue( arg0.getSearchValue() );
        arg1.setCreateDept( arg0.getCreateDept() );
        arg1.setCreateBy( arg0.getCreateBy() );
        arg1.setCreateTime( arg0.getCreateTime() );
        arg1.setUpdateBy( arg0.getUpdateBy() );
        arg1.setUpdateTime( arg0.getUpdateTime() );
        if ( arg1.getParams() != null ) {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.getParams().clear();
                arg1.getParams().putAll( map );
            }
            else {
                arg1.setParams( null );
            }
        }
        else {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.setParams( new LinkedHashMap<String, Object>( map ) );
            }
        }
        arg1.setId( arg0.getId() );
        arg1.setNewCommernt( arg0.getNewCommernt() );

        return arg1;
    }
}
