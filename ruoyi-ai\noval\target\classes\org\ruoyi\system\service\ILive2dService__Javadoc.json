{"doc": " live2dService接口\n\n <AUTHOR>\n @date 2025-06-19\n", "fields": [], "enumConstants": [], "methods": [{"name": "queryById", "paramTypes": ["java.lang.Long"], "doc": " 查询live2d\n"}, {"name": "queryPageList", "paramTypes": ["org.ruoyi.system.domain.bo.Live2dBo", "org.ruoyi.core.page.PageQuery"], "doc": " 查询live2d列表\n"}, {"name": "queryList", "paramTypes": ["org.ruoyi.system.domain.bo.Live2dBo"], "doc": " 查询live2d列表\n"}, {"name": "insertByBo", "paramTypes": ["org.ruoyi.system.domain.bo.Live2dBo"], "doc": " 新增live2d\n"}, {"name": "updateByBo", "paramTypes": ["org.ruoyi.system.domain.bo.Live2dBo"], "doc": " 修改live2d\n"}, {"name": "deleteWithValidByIds", "paramTypes": ["java.util.Collection", "java.lang.Bo<PERSON>an"], "doc": " 校验并批量删除live2d信息\n"}], "constructors": []}