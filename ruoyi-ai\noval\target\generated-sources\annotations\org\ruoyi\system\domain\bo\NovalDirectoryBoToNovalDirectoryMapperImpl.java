package org.ruoyi.system.domain.bo;

import java.util.LinkedHashMap;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.ruoyi.system.domain.NovalDirectory;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-12T10:45:12+0800",
    comments = "version: 1.5.3.Final, compiler: javac, environment: Java 17.0.12 (Oracle Corporation)"
)
@Component
public class NovalDirectoryBoToNovalDirectoryMapperImpl implements NovalDirectoryBoToNovalDirectoryMapper {

    @Override
    public NovalDirectory convert(NovalDirectoryBo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        NovalDirectory novalDirectory = new NovalDirectory();

        novalDirectory.setSearchValue( arg0.getSearchValue() );
        novalDirectory.setCreateDept( arg0.getCreateDept() );
        novalDirectory.setCreateBy( arg0.getCreateBy() );
        novalDirectory.setCreateTime( arg0.getCreateTime() );
        novalDirectory.setUpdateBy( arg0.getUpdateBy() );
        novalDirectory.setUpdateTime( arg0.getUpdateTime() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            novalDirectory.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        novalDirectory.setId( arg0.getId() );
        novalDirectory.setChapter( arg0.getChapter() );
        novalDirectory.setLink( arg0.getLink() );
        novalDirectory.setChaptername( arg0.getChaptername() );

        return novalDirectory;
    }

    @Override
    public NovalDirectory convert(NovalDirectoryBo arg0, NovalDirectory arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setSearchValue( arg0.getSearchValue() );
        arg1.setCreateDept( arg0.getCreateDept() );
        arg1.setCreateBy( arg0.getCreateBy() );
        arg1.setCreateTime( arg0.getCreateTime() );
        arg1.setUpdateBy( arg0.getUpdateBy() );
        arg1.setUpdateTime( arg0.getUpdateTime() );
        if ( arg1.getParams() != null ) {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.getParams().clear();
                arg1.getParams().putAll( map );
            }
            else {
                arg1.setParams( null );
            }
        }
        else {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.setParams( new LinkedHashMap<String, Object>( map ) );
            }
        }
        arg1.setId( arg0.getId() );
        arg1.setChapter( arg0.getChapter() );
        arg1.setLink( arg0.getLink() );
        arg1.setChaptername( arg0.getChaptername() );

        return arg1;
    }
}
