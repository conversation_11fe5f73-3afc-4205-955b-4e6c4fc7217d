package org.ruoyi.system.domain.bo;

import java.util.LinkedHashMap;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.ruoyi.system.domain.NewsComment;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-12T10:45:13+0800",
    comments = "version: 1.5.3.Final, compiler: javac, environment: Java 17.0.12 (Oracle Corporation)"
)
@Component
public class NewsCommentBoToNewsCommentMapperImpl implements NewsCommentBoToNewsCommentMapper {

    @Override
    public NewsComment convert(NewsCommentBo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        NewsComment newsComment = new NewsComment();

        newsComment.setSearchValue( arg0.getSearchValue() );
        newsComment.setCreateDept( arg0.getCreateDept() );
        newsComment.setCreateBy( arg0.getCreateBy() );
        newsComment.setCreateTime( arg0.getCreateTime() );
        newsComment.setUpdateBy( arg0.getUpdateBy() );
        newsComment.setUpdateTime( arg0.getUpdateTime() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            newsComment.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        newsComment.setNewsId( arg0.getNewsId() );
        newsComment.setCommerntId( arg0.getCommerntId() );

        return newsComment;
    }

    @Override
    public NewsComment convert(NewsCommentBo arg0, NewsComment arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setSearchValue( arg0.getSearchValue() );
        arg1.setCreateDept( arg0.getCreateDept() );
        arg1.setCreateBy( arg0.getCreateBy() );
        arg1.setCreateTime( arg0.getCreateTime() );
        arg1.setUpdateBy( arg0.getUpdateBy() );
        arg1.setUpdateTime( arg0.getUpdateTime() );
        if ( arg1.getParams() != null ) {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.getParams().clear();
                arg1.getParams().putAll( map );
            }
            else {
                arg1.setParams( null );
            }
        }
        else {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.setParams( new LinkedHashMap<String, Object>( map ) );
            }
        }
        arg1.setNewsId( arg0.getNewsId() );
        arg1.setCommerntId( arg0.getCommerntId() );

        return arg1;
    }
}
