package org.ruoyi.system.domain.bo;

import java.util.LinkedHashMap;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.ruoyi.system.domain.Live2dcontroller;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-12T10:45:13+0800",
    comments = "version: 1.5.3.Final, compiler: javac, environment: Java 17.0.12 (Oracle Corporation)"
)
@Component
public class Live2dcontrollerBoToLive2dcontrollerMapperImpl implements Live2dcontrollerBoToLive2dcontrollerMapper {

    @Override
    public Live2dcontroller convert(Live2dcontrollerBo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        Live2dcontroller live2dcontroller = new Live2dcontroller();

        live2dcontroller.setSearchValue( arg0.getSearchValue() );
        live2dcontroller.setCreateDept( arg0.getCreateDept() );
        live2dcontroller.setCreateBy( arg0.getCreateBy() );
        live2dcontroller.setCreateTime( arg0.getCreateTime() );
        live2dcontroller.setUpdateBy( arg0.getUpdateBy() );
        live2dcontroller.setUpdateTime( arg0.getUpdateTime() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            live2dcontroller.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        live2dcontroller.setId( arg0.getId() );
        live2dcontroller.setUserid( arg0.getUserid() );
        live2dcontroller.setName( arg0.getName() );

        return live2dcontroller;
    }

    @Override
    public Live2dcontroller convert(Live2dcontrollerBo arg0, Live2dcontroller arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setSearchValue( arg0.getSearchValue() );
        arg1.setCreateDept( arg0.getCreateDept() );
        arg1.setCreateBy( arg0.getCreateBy() );
        arg1.setCreateTime( arg0.getCreateTime() );
        arg1.setUpdateBy( arg0.getUpdateBy() );
        arg1.setUpdateTime( arg0.getUpdateTime() );
        if ( arg1.getParams() != null ) {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.getParams().clear();
                arg1.getParams().putAll( map );
            }
            else {
                arg1.setParams( null );
            }
        }
        else {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.setParams( new LinkedHashMap<String, Object>( map ) );
            }
        }
        arg1.setId( arg0.getId() );
        arg1.setUserid( arg0.getUserid() );
        arg1.setName( arg0.getName() );

        return arg1;
    }
}
