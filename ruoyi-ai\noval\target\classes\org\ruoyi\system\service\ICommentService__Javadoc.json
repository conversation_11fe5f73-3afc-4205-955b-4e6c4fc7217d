{"doc": " 评论Service接口\n\n <AUTHOR>\n @date 2025-06-13\n", "fields": [], "enumConstants": [], "methods": [{"name": "queryLandlordById", "paramTypes": ["java.lang.Long"], "doc": " 查询评论\n"}, {"name": "queryPageList", "paramTypes": ["org.ruoyi.system.domain.bo.CommentBo", "org.ruoyi.core.page.PageQuery"], "doc": " 查询评论列表\n"}, {"name": "queryList", "paramTypes": ["org.ruoyi.system.domain.bo.CommentBo"], "doc": " 查询评论列表\n"}, {"name": "insertByBo", "paramTypes": ["org.ruoyi.system.domain.bo.CommentBo"], "doc": " 新增评论\n"}, {"name": "updateByBo", "paramTypes": ["org.ruoyi.system.domain.bo.CommentBo"], "doc": " 修改评论\n"}, {"name": "deleteWithValidByIds", "paramTypes": ["java.util.Collection", "java.lang.Bo<PERSON>an"], "doc": " 校验并批量删除评论信息\n"}], "constructors": []}