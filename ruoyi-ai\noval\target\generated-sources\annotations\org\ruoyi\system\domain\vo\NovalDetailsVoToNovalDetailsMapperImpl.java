package org.ruoyi.system.domain.vo;

import javax.annotation.processing.Generated;
import org.ruoyi.system.domain.NovalDetails;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-12T10:45:13+0800",
    comments = "version: 1.5.3.Final, compiler: javac, environment: Java 17.0.12 (Oracle Corporation)"
)
@Component
public class NovalDetailsVoToNovalDetailsMapperImpl implements NovalDetailsVoToNovalDetailsMapper {

    @Override
    public NovalDetails convert(NovalDetailsVo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        NovalDetails novalDetails = new NovalDetails();

        novalDetails.setId( arg0.getId() );
        novalDetails.setGrades( arg0.getGrades() );
        novalDetails.setType( arg0.getType() );
        novalDetails.setPlatform( arg0.getPlatform() );
        novalDetails.setSummary( arg0.getSummary() );
        novalDetails.setIcon( arg0.getIcon() );
        novalDetails.setLook( arg0.getLook() );

        return novalDetails;
    }

    @Override
    public NovalDetails convert(NovalDetailsVo arg0, NovalDetails arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setGrades( arg0.getGrades() );
        arg1.setType( arg0.getType() );
        arg1.setPlatform( arg0.getPlatform() );
        arg1.setSummary( arg0.getSummary() );
        arg1.setIcon( arg0.getIcon() );
        arg1.setLook( arg0.getLook() );

        return arg1;
    }
}
