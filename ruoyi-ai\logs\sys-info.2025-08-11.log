2025-08-11 09:14:47 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-08-11 09:14:47 [main] INFO  org.ruoyi.RuoYiAIApplication - Starting RuoYiAIApplication using Java 17.0.12 with PID 6048 (D:\daima\new\ruoyi-ai\ruoyi-admin\target\classes started by 86153 in D:\daima\new\ruoyi-ai)
2025-08-11 09:14:47 [main] INFO  org.ruoyi.RuoYiAIApplication - The following 1 profile is active: "dev"
2025-08-11 09:15:02 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-08-11 09:15:04 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-08-11 09:15:04 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-08-11 09:15:05 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@4eb69e8d
2025-08-11 09:15:05 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-08-11 09:15:05 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-08-11 09:15:05 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-08-11 09:15:11 [main] INFO  o.r.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-08-11 09:15:12 [main] INFO  o.r.common.redis.config.RedisConfig - 初始化 redis 配置
2025-08-11 09:15:12 [main] INFO  org.redisson.Version - Redisson 3.20.1
2025-08-11 09:15:13 [redisson-netty-2-4] INFO  o.r.c.p.MasterPubSubConnectionPool - 1 connections initialized for 127.0.0.1/127.0.0.1:6379
2025-08-11 09:15:13 [redisson-netty-2-3] INFO  o.r.c.pool.MasterConnectionPool - 8 connections initialized for 127.0.0.1/127.0.0.1:6379
2025-08-11 09:15:15 [main] INFO  o.r.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-08-11 09:15:21 [main] INFO  io.undertow - starting server: Undertow - 2.3.18.Final
2025-08-11 09:15:21 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-08-11 09:15:21 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-08-11 09:15:22 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-08-11 09:15:22 [main] INFO  org.ruoyi.RuoYiAIApplication - Started RuoYiAIApplication in 35.417 seconds (process running for 56.399)
2025-08-11 09:15:22 [main] INFO  o.r.c.c.l.WebSocketTopicListener - 初始化WebSocket主题订阅监听器成功
2025-08-11 09:15:22 [main] INFO  o.r.s.runner.SystemApplicationRunner - 初始化OSS配置成功
2025-08-11 09:43:12 [XNIO-1 task-3] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-11 09:43:12 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-11 09:43:12 [XNIO-1 task-3] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-11 09:43:13 [XNIO-1 task-3] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-11 09:43:15 [XNIO-1 task-3] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-11 09:43:42 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-11 09:43:42 [XNIO-1 task-3] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-11 09:43:42 [XNIO-1 task-4] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-11 09:43:42 [XNIO-1 task-5] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-11 09:44:12 [XNIO-1 task-5] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-11 09:44:12 [XNIO-1 task-2] INFO  o.r.c.w.i.PlusWebInvokeTimeInterceptor - 域名信息：127.0.0.1
2025-08-11 09:45:48 [SpringApplicationShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.18.Final
2025-08-11 09:45:48 [SpringApplicationShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-08-11 09:45:50 [SpringApplicationShutdownHook] INFO  o.r.c.core.manager.ShutdownManager - ====关闭后台任务任务线程池====
2025-08-11 09:45:50 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-08-11 09:45:50 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown initiated...
2025-08-11 09:45:50 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown completed.
2025-08-11 09:45:50 [SpringApplicationShutdownHook] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [master] success,
2025-08-11 09:45:50 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2025-08-11 10:21:12 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-08-11 10:21:12 [main] INFO  org.ruoyi.RuoYiAIApplication - Starting RuoYiAIApplication using Java 17.0.12 with PID 19924 (D:\daima\new\ruoyi-ai\ruoyi-admin\target\classes started by 86153 in D:\daima\new\ruoyi-ai)
2025-08-11 10:21:12 [main] INFO  org.ruoyi.RuoYiAIApplication - The following 1 profile is active: "dev"
2025-08-11 10:21:18 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-08-11 10:21:19 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-08-11 10:21:19 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-08-11 10:21:19 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@6edc8385
2025-08-11 10:21:19 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-08-11 10:21:19 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-08-11 10:21:19 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-08-11 10:21:22 [main] INFO  o.r.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-08-11 10:21:22 [main] INFO  o.r.common.redis.config.RedisConfig - 初始化 redis 配置
2025-08-11 10:21:22 [main] INFO  org.redisson.Version - Redisson 3.20.1
2025-08-11 10:21:23 [redisson-netty-2-4] INFO  o.r.c.p.MasterPubSubConnectionPool - 1 connections initialized for 127.0.0.1/127.0.0.1:6379
2025-08-11 10:21:23 [redisson-netty-2-3] INFO  o.r.c.pool.MasterConnectionPool - 8 connections initialized for 127.0.0.1/127.0.0.1:6379
2025-08-11 10:21:24 [main] INFO  o.r.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-08-11 10:21:28 [main] INFO  io.undertow - starting server: Undertow - 2.3.18.Final
2025-08-11 10:21:28 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-08-11 10:21:28 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-08-11 10:21:28 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-08-11 10:21:28 [main] INFO  org.ruoyi.RuoYiAIApplication - Started RuoYiAIApplication in 16.879 seconds (process running for 18.284)
2025-08-11 10:21:29 [main] INFO  o.r.c.c.l.WebSocketTopicListener - 初始化WebSocket主题订阅监听器成功
2025-08-11 10:21:29 [main] INFO  o.r.s.runner.SystemApplicationRunner - 初始化OSS配置成功
2025-08-11 10:21:29 [RMI TCP Connection(6)-172.18.0.1] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-11 10:31:53 [SpringApplicationShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.18.Final
2025-08-11 10:31:53 [SpringApplicationShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-08-11 10:31:55 [SpringApplicationShutdownHook] INFO  o.r.c.core.manager.ShutdownManager - ====关闭后台任务任务线程池====
2025-08-11 10:31:55 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-08-11 10:31:55 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown initiated...
2025-08-11 10:31:55 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown completed.
2025-08-11 10:31:55 [SpringApplicationShutdownHook] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [master] success,
2025-08-11 10:31:55 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2025-08-11 10:54:41 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-08-11 10:54:41 [main] INFO  org.ruoyi.RuoYiAIApplication - Starting RuoYiAIApplication using Java 17.0.12 with PID 7948 (D:\daima\new\ruoyi-ai\ruoyi-admin\target\classes started by 86153 in D:\daima\new\ruoyi-ai)
2025-08-11 10:54:41 [main] INFO  org.ruoyi.RuoYiAIApplication - The following 1 profile is active: "dev"
2025-08-11 10:54:49 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-08-11 10:54:49 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-08-11 10:54:49 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-08-11 10:54:50 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@2a10407e
2025-08-11 10:54:50 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-08-11 10:54:50 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-08-11 10:54:50 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-08-11 10:54:53 [main] INFO  o.r.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-08-11 10:54:53 [main] INFO  o.r.common.redis.config.RedisConfig - 初始化 redis 配置
2025-08-11 10:54:53 [main] INFO  org.redisson.Version - Redisson 3.20.1
2025-08-11 10:54:54 [redisson-netty-2-5] INFO  o.r.c.p.MasterPubSubConnectionPool - 1 connections initialized for 127.0.0.1/127.0.0.1:6379
2025-08-11 10:54:54 [redisson-netty-2-3] INFO  o.r.c.pool.MasterConnectionPool - 8 connections initialized for 127.0.0.1/127.0.0.1:6379
2025-08-11 10:54:55 [main] INFO  o.r.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-08-11 10:55:01 [main] INFO  io.undertow - starting server: Undertow - 2.3.18.Final
2025-08-11 10:55:01 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-08-11 10:55:01 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-08-11 10:55:01 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-08-11 10:55:01 [main] INFO  org.ruoyi.RuoYiAIApplication - Started RuoYiAIApplication in 21.017 seconds (process running for 22.5)
2025-08-11 10:55:01 [main] INFO  o.r.c.c.l.WebSocketTopicListener - 初始化WebSocket主题订阅监听器成功
2025-08-11 10:55:02 [main] INFO  o.r.s.runner.SystemApplicationRunner - 初始化OSS配置成功
2025-08-11 10:55:02 [RMI TCP Connection(6)-172.30.16.1] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-11 21:01:33 [SpringApplicationShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.18.Final
2025-08-11 21:01:33 [SpringApplicationShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-08-11 21:01:35 [SpringApplicationShutdownHook] INFO  o.r.c.core.manager.ShutdownManager - ====关闭后台任务任务线程池====
2025-08-11 21:01:35 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-08-11 21:01:35 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown initiated...
2025-08-11 21:01:35 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown completed.
2025-08-11 21:01:35 [SpringApplicationShutdownHook] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [master] success,
2025-08-11 21:01:35 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
