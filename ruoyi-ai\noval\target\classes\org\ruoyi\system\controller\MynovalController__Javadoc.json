{"doc": " 我的小说\n\n <AUTHOR>\n @date 2025-07-16\n", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["java.lang.Long", "org.ruoyi.core.page.PageQuery"], "doc": " 查询我的小说列表\n"}, {"name": "export", "paramTypes": ["java.lang.Long", "jakarta.servlet.http.HttpServletResponse"], "doc": " 导出我的小说列表\n"}, {"name": "getInfo", "paramTypes": ["java.lang.Long"], "doc": " 获取我的小说详细信息\n\n @param id 主键\n"}, {"name": "add", "paramTypes": ["org.ruoyi.system.domain.bo.MynovalBo"], "doc": " 新增我的小说\n"}, {"name": "edit", "paramTypes": ["org.ruoyi.system.domain.bo.NovalBo"], "doc": " 修改我的小说\n"}, {"name": "remove", "paramTypes": ["java.lang.Long[]"], "doc": " 删除我的小说\n\n @param ids 主键串\n"}], "constructors": []}