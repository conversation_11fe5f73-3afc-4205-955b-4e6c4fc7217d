{"doc": " 我的小说Service接口\n\n <AUTHOR>\n @date 2025-07-16\n", "fields": [], "enumConstants": [], "methods": [{"name": "queryById", "paramTypes": ["java.lang.Long"], "doc": " 查询我的小说\n"}, {"name": "queryPageList", "paramTypes": ["java.lang.Long", "org.ruoyi.core.page.PageQuery"], "doc": " 查询我的小说列表\n"}, {"name": "queryList", "paramTypes": ["java.lang.Long"], "doc": " 查询我的小说列表\n"}, {"name": "insertByBo", "paramTypes": ["org.ruoyi.system.domain.bo.MynovalBo"], "doc": " 新增我的小说\n"}, {"name": "updateByBo", "paramTypes": ["org.ruoyi.system.domain.bo.NovalBo"], "doc": " 修改我的小说\n"}, {"name": "deleteWithValidByIds", "paramTypes": ["java.util.Collection", "java.lang.Bo<PERSON>an"], "doc": " 校验并批量删除我的小说信息\n"}], "constructors": []}