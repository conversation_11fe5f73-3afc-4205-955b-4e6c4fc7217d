package org.ruoyi.system.domain;

import javax.annotation.processing.Generated;
import org.ruoyi.system.domain.vo.NewsCommentVo;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-12T10:45:13+0800",
    comments = "version: 1.5.3.Final, compiler: javac, environment: Java 17.0.12 (Oracle Corporation)"
)
@Component
public class NewsCommentToNewsCommentVoMapperImpl implements NewsCommentToNewsCommentVoMapper {

    @Override
    public NewsCommentVo convert(NewsComment arg0) {
        if ( arg0 == null ) {
            return null;
        }

        NewsCommentVo newsCommentVo = new NewsCommentVo();

        newsCommentVo.setNewsId( arg0.getNewsId() );
        newsCommentVo.setCommerntId( arg0.getCommerntId() );

        return newsCommentVo;
    }

    @Override
    public NewsCommentVo convert(NewsComment arg0, NewsCommentVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setNewsId( arg0.getNewsId() );
        arg1.setCommerntId( arg0.getCommerntId() );

        return arg1;
    }
}
