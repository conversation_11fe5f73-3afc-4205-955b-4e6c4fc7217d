{"doc": " 关注\n\n <AUTHOR>\n @date 2025-06-17\n", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["org.ruoyi.system.domain.bo.AttentionBo", "org.ruoyi.core.page.PageQuery"], "doc": " 查询关注列表\n"}, {"name": "export", "paramTypes": ["org.ruoyi.system.domain.bo.AttentionBo", "jakarta.servlet.http.HttpServletResponse"], "doc": " 导出关注列表\n"}, {"name": "getInfo", "paramTypes": ["java.lang.String"], "doc": " 获取关注详细信息\n\n @param id 主键\n"}, {"name": "add", "paramTypes": ["org.ruoyi.system.domain.bo.AttentionBo"], "doc": " 新增关注\n"}, {"name": "edit", "paramTypes": ["org.ruoyi.system.domain.bo.AttentionBo"], "doc": " 修改关注\n"}, {"name": "remove", "paramTypes": ["java.lang.String[]"], "doc": " 删除关注\n\n @param ids 主键串\n"}], "constructors": []}