{"doc": " 小说目录Service业务层处理\n\n <AUTHOR> \n @date 2025-06-07\n", "fields": [], "enumConstants": [], "methods": [{"name": "queryById", "paramTypes": ["java.lang.String"], "doc": " 查询小说目录\n"}, {"name": "queryPageList", "paramTypes": ["org.ruoyi.system.domain.bo.NovalDirectoryBo", "org.ruoyi.core.page.PageQuery"], "doc": " 查询小说目录列表\n"}, {"name": "queryList", "paramTypes": ["org.ruoyi.system.domain.bo.NovalDirectoryBo"], "doc": " 查询小说目录列表\n"}, {"name": "insertByBo", "paramTypes": ["org.ruoyi.system.domain.bo.NovalDirectoryBo"], "doc": " 新增小说目录\n"}, {"name": "updateByBo", "paramTypes": ["org.ruoyi.system.domain.bo.NovalDirectoryBo"], "doc": " 修改小说目录\n"}, {"name": "validEntityBeforeSave", "paramTypes": ["org.ruoyi.system.domain.NovalDirectory"], "doc": " 保存前的数据校验\n"}, {"name": "deleteWithValidByIds", "paramTypes": ["java.util.Collection", "java.lang.Bo<PERSON>an"], "doc": " 批量删除小说目录\n"}], "constructors": []}