package org.ruoyi.system.domain.bo;

import java.util.LinkedHashMap;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.ruoyi.system.domain.Live2d;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-12T10:45:13+0800",
    comments = "version: 1.5.3.Final, compiler: javac, environment: Java 17.0.12 (Oracle Corporation)"
)
@Component
public class Live2dBoToLive2dMapperImpl implements Live2dBoToLive2dMapper {

    @Override
    public Live2d convert(Live2dBo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        Live2d live2d = new Live2d();

        live2d.setSearchValue( arg0.getSearchValue() );
        live2d.setCreateDept( arg0.getCreateDept() );
        live2d.setCreateBy( arg0.getCreateBy() );
        live2d.setCreateTime( arg0.getCreateTime() );
        live2d.setUpdateBy( arg0.getUpdateBy() );
        live2d.setUpdateTime( arg0.getUpdateTime() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            live2d.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        live2d.setId( arg0.getId() );
        live2d.setName( arg0.getName() );
        live2d.setType( arg0.getType() );
        live2d.setOriginalName( arg0.getOriginalName() );

        return live2d;
    }

    @Override
    public Live2d convert(Live2dBo arg0, Live2d arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setSearchValue( arg0.getSearchValue() );
        arg1.setCreateDept( arg0.getCreateDept() );
        arg1.setCreateBy( arg0.getCreateBy() );
        arg1.setCreateTime( arg0.getCreateTime() );
        arg1.setUpdateBy( arg0.getUpdateBy() );
        arg1.setUpdateTime( arg0.getUpdateTime() );
        if ( arg1.getParams() != null ) {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.getParams().clear();
                arg1.getParams().putAll( map );
            }
            else {
                arg1.setParams( null );
            }
        }
        else {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.setParams( new LinkedHashMap<String, Object>( map ) );
            }
        }
        arg1.setId( arg0.getId() );
        arg1.setName( arg0.getName() );
        arg1.setType( arg0.getType() );
        arg1.setOriginalName( arg0.getOriginalName() );

        return arg1;
    }
}
