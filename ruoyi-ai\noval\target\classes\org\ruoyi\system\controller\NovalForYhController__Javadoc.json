{"doc": " 用户浏览书库\n\n <AUTHOR>\n @date 2025-06-11\n", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["org.ruoyi.system.domain.bo.NovalForYhBo", "org.ruoyi.core.page.PageQuery"], "doc": " 查询用户浏览书库列表\n"}, {"name": "getInfo", "paramTypes": ["java.lang.Long"], "doc": " 获取用户浏览书库详细信息\n\n @param id 主键\n"}, {"name": "suggestNovalNames", "paramTypes": ["java.lang.String"], "doc": " 自动补全建议接口（增强版）\n 支持书名、作者、类型等多字段智能补全\n\n @param keyword 用户输入的关键词\n @return 自动补全建议列表\n"}, {"name": "checkIndexStatus", "paramTypes": [], "doc": " 检查ES索引状态和自动补全配置\n"}], "constructors": []}