{"doc": " 评论\n\n <AUTHOR>\n @date 2025-06-13\n", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["org.ruoyi.system.domain.bo.CommentBo", "org.ruoyi.core.page.PageQuery"], "doc": " 查询评论列表\n"}, {"name": "export", "paramTypes": ["org.ruoyi.system.domain.bo.CommentBo", "jakarta.servlet.http.HttpServletResponse"], "doc": " 导出评论列表\n"}, {"name": "getInfo", "paramTypes": ["java.lang.Long"], "doc": " 获取评论详细信息\n\n @param commerntId 主键\n"}, {"name": "getInfo2", "paramTypes": ["java.lang.Long"], "doc": " 获取用户帖子\n\n @param userid 主键\n"}, {"name": "add", "paramTypes": ["org.ruoyi.system.domain.bo.CommentBo"], "doc": " 新增评论\n"}, {"name": "edit", "paramTypes": ["org.ruoyi.system.domain.bo.CommentBo"], "doc": " 修改评论\n"}, {"name": "remove", "paramTypes": ["java.lang.Long[]"], "doc": " 删除评论\n\n @param commerntIds 主键串\n"}], "constructors": []}