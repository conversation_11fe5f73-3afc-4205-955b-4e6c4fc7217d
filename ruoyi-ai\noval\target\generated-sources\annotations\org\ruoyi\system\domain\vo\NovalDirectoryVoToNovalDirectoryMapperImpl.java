package org.ruoyi.system.domain.vo;

import javax.annotation.processing.Generated;
import org.ruoyi.system.domain.NovalDirectory;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-12T10:45:13+0800",
    comments = "version: 1.5.3.Final, compiler: javac, environment: Java 17.0.12 (Oracle Corporation)"
)
@Component
public class NovalDirectoryVoToNovalDirectoryMapperImpl implements NovalDirectoryVoToNovalDirectoryMapper {

    @Override
    public NovalDirectory convert(NovalDirectoryVo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        NovalDirectory novalDirectory = new NovalDirectory();

        novalDirectory.setId( arg0.getId() );
        novalDirectory.setChapter( arg0.getChapter() );
        novalDirectory.setLink( arg0.getLink() );
        novalDirectory.setChaptername( arg0.getChaptername() );

        return novalDirectory;
    }

    @Override
    public NovalDirectory convert(NovalDirectoryVo arg0, NovalDirectory arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setChapter( arg0.getChapter() );
        arg1.setLink( arg0.getLink() );
        arg1.setChaptername( arg0.getChaptername() );

        return arg1;
    }
}
