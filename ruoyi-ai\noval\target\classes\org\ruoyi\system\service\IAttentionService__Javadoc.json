{"doc": " 关注Service接口\n\n <AUTHOR>\n @date 2025-06-17\n", "fields": [], "enumConstants": [], "methods": [{"name": "queryById", "paramTypes": ["java.lang.String"], "doc": " 查询关注\n"}, {"name": "queryPageList", "paramTypes": ["org.ruoyi.system.domain.bo.AttentionBo", "org.ruoyi.core.page.PageQuery"], "doc": " 查询关注列表\n"}, {"name": "queryList", "paramTypes": ["org.ruoyi.system.domain.bo.AttentionBo"], "doc": " 查询关注列表\n"}, {"name": "insertByBo", "paramTypes": ["org.ruoyi.system.domain.bo.AttentionBo"], "doc": " 新增关注\n"}, {"name": "updateByBo", "paramTypes": ["org.ruoyi.system.domain.bo.AttentionBo"], "doc": " 修改关注\n"}, {"name": "deleteWithValidByIds", "paramTypes": ["java.util.Collection", "java.lang.Bo<PERSON>an"], "doc": " 校验并批量删除关注信息\n"}, {"name": "checkAttention", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": " 检查是否互相关注\n @param id\n @param otherId\n @return\n"}], "constructors": []}