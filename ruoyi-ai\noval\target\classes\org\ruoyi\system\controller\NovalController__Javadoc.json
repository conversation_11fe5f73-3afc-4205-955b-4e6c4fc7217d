{"doc": " 书库管理\n\n <AUTHOR> \n @date 2025-06-06\n", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["org.ruoyi.system.domain.bo.NovalBo", "org.ruoyi.core.page.PageQuery"], "doc": " 查询书库管理列表\n"}, {"name": "export", "paramTypes": ["org.ruoyi.system.domain.bo.NovalBo", "jakarta.servlet.http.HttpServletResponse"], "doc": " 导出书库管理列表\n"}, {"name": "getInfo", "paramTypes": ["java.lang.Long"], "doc": " 获取书库管理详细信息\n\n @param id 主键\n"}, {"name": "add", "paramTypes": ["org.ruoyi.system.domain.bo.NovalBo"], "doc": " 新增书库管理\n"}, {"name": "edit", "paramTypes": ["org.ruoyi.system.domain.bo.NovalBo"], "doc": " 修改书库管理\n"}, {"name": "remove", "paramTypes": ["java.lang.Long[]"], "doc": " 删除书库管理\n\n @param ids 主键串\n"}, {"name": "weekAdd", "paramTypes": [], "doc": " 本周新增书籍\n"}], "constructors": []}