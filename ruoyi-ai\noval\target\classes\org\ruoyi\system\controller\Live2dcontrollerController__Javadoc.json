{"doc": " 用户个性live2d控制\n\n <AUTHOR>\n @date 2025-06-24\n", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["org.ruoyi.system.domain.bo.Live2dcontrollerBo", "org.ruoyi.core.page.PageQuery"], "doc": " 查询用户个性live2d控制列表\n"}, {"name": "export", "paramTypes": ["org.ruoyi.system.domain.bo.Live2dcontrollerBo", "jakarta.servlet.http.HttpServletResponse"], "doc": " 导出用户个性live2d控制列表\n"}, {"name": "getInfo", "paramTypes": ["java.lang.Long"], "doc": " 获取用户个性live2d控制详细信息\n\n @param id 主键\n"}, {"name": "add", "paramTypes": ["org.ruoyi.system.domain.bo.Live2dcontrollerBo"], "doc": " 新增用户个性live2d控制\n"}, {"name": "edit", "paramTypes": ["org.ruoyi.system.domain.bo.Live2dcontrollerBo"], "doc": " 修改用户个性live2d控制\n"}, {"name": "remove", "paramTypes": ["java.lang.Long[]"], "doc": " 删除用户个性live2d控制\n\n @param ids 主键串\n"}], "constructors": []}