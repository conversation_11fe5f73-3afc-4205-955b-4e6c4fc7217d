{"doc": " 关注Service业务层处理\n\n <AUTHOR>\n @date 2025-06-17\n", "fields": [], "enumConstants": [], "methods": [{"name": "queryById", "paramTypes": ["java.lang.String"], "doc": " 查询关注\n"}, {"name": "queryPageList", "paramTypes": ["org.ruoyi.system.domain.bo.AttentionBo", "org.ruoyi.core.page.PageQuery"], "doc": " 查询关注列表\n"}, {"name": "queryList", "paramTypes": ["org.ruoyi.system.domain.bo.AttentionBo"], "doc": " 查询关注列表\n"}, {"name": "insertByBo", "paramTypes": ["org.ruoyi.system.domain.bo.AttentionBo"], "doc": " 新增关注\n"}, {"name": "updateByBo", "paramTypes": ["org.ruoyi.system.domain.bo.AttentionBo"], "doc": " 修改关注\n"}, {"name": "validEntityBeforeSave", "paramTypes": ["org.ruoyi.system.domain.Attention"], "doc": " 保存前的数据校验\n"}, {"name": "deleteWithValidByIds", "paramTypes": ["java.util.Collection", "java.lang.Bo<PERSON>an"], "doc": " 批量删除关注\n"}], "constructors": []}