{"doc": " 小说详细\n\n <AUTHOR> \n @date 2025-06-07\n", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["org.ruoyi.system.domain.bo.NovalDetailsBo", "org.ruoyi.core.page.PageQuery"], "doc": " 查询小说详细列表\n"}, {"name": "export", "paramTypes": ["org.ruoyi.system.domain.bo.NovalDetailsBo", "jakarta.servlet.http.HttpServletResponse"], "doc": " 导出小说详细列表\n"}, {"name": "getInfo", "paramTypes": ["java.lang.Long"], "doc": " 获取小说详细详细信息\n\n @param id 主键\n"}, {"name": "add", "paramTypes": ["org.ruoyi.system.domain.bo.NovalDetailsBo"], "doc": " 新增小说详细\n"}, {"name": "edit", "paramTypes": ["org.ruoyi.system.domain.bo.NovalDetailsBo"], "doc": " 修改小说详细\n"}, {"name": "remove", "paramTypes": ["java.lang.Long[]"], "doc": " 删除小说详细\n\n @param ids 主键串\n"}], "constructors": []}