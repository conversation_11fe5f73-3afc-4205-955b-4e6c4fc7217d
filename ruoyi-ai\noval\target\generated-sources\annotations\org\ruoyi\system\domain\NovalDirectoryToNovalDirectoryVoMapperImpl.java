package org.ruoyi.system.domain;

import javax.annotation.processing.Generated;
import org.ruoyi.system.domain.vo.NovalDirectoryVo;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-12T10:45:13+0800",
    comments = "version: 1.5.3.Final, compiler: javac, environment: Java 17.0.12 (Oracle Corporation)"
)
@Component
public class NovalDirectoryToNovalDirectoryVoMapperImpl implements NovalDirectoryToNovalDirectoryVoMapper {

    @Override
    public NovalDirectoryVo convert(NovalDirectory arg0) {
        if ( arg0 == null ) {
            return null;
        }

        NovalDirectoryVo novalDirectoryVo = new NovalDirectoryVo();

        novalDirectoryVo.setId( arg0.getId() );
        novalDirectoryVo.setChapter( arg0.getChapter() );
        novalDirectoryVo.setLink( arg0.getLink() );
        novalDirectoryVo.setChaptername( arg0.getChaptername() );

        return novalDirectoryVo;
    }

    @Override
    public NovalDirectoryVo convert(NovalDirectory arg0, NovalDirectoryVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setChapter( arg0.getChapter() );
        arg1.setLink( arg0.getLink() );
        arg1.setChaptername( arg0.getChaptername() );

        return arg1;
    }
}
