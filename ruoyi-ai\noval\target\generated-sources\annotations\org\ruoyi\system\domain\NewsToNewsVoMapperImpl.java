package org.ruoyi.system.domain;

import java.text.SimpleDateFormat;
import javax.annotation.processing.Generated;
import org.ruoyi.system.domain.vo.NewsVo;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-12T10:45:13+0800",
    comments = "version: 1.5.3.Final, compiler: javac, environment: Java 17.0.12 (Oracle Corporation)"
)
@Component
public class NewsToNewsVoMapperImpl implements NewsToNewsVoMapper {

    @Override
    public NewsVo convert(News arg0) {
        if ( arg0 == null ) {
            return null;
        }

        NewsVo newsVo = new NewsVo();

        newsVo.setId( arg0.getId() );
        newsVo.setOtherId( arg0.getOtherId() );
        newsVo.setMyId( arg0.getMyId() );
        newsVo.setNews( arg0.getNews() );
        newsVo.setType( arg0.getType() );
        newsVo.setIsread( arg0.getIsread() );
        if ( arg0.getCreateTime() != null ) {
            newsVo.setCreateTime( new SimpleDateFormat().format( arg0.getCreateTime() ) );
        }

        return newsVo;
    }

    @Override
    public NewsVo convert(News arg0, NewsVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setOtherId( arg0.getOtherId() );
        arg1.setMyId( arg0.getMyId() );
        arg1.setNews( arg0.getNews() );
        arg1.setType( arg0.getType() );
        arg1.setIsread( arg0.getIsread() );
        if ( arg0.getCreateTime() != null ) {
            arg1.setCreateTime( new SimpleDateFormat().format( arg0.getCreateTime() ) );
        }
        else {
            arg1.setCreateTime( null );
        }

        return arg1;
    }
}
