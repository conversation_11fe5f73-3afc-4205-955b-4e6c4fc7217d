{"doc": " 布谷鸟过滤器工具类\n 兼容最新稳定版CuckooFilter4J\n", "fields": [], "enumConstants": [], "methods": [{"name": "afterPropertiesSet", "paramTypes": [], "doc": " 初始化布谷鸟过滤器\n"}, {"name": "setFunnel", "paramTypes": ["com.google.common.hash.Funnel"], "doc": " 设置Funnel(用于非Long类型的处理)\n"}, {"name": "add", "paramTypes": ["java.lang.Object"], "doc": " 添加元素\n @param element 要添加的元素\n @return 是否添加成功\n"}, {"name": "addAll", "paramTypes": ["java.util.Collection"], "doc": " 批量添加元素\n @param elements 元素集合\n"}, {"name": "contains", "paramTypes": ["java.lang.Object"], "doc": " 检查元素是否存在\n @param element 要检查的元素\n @return 是否存在(可能有误判)\n"}, {"name": "remove", "paramTypes": ["java.lang.Object"], "doc": " 删除元素\n @param element 要删除的元素\n @return 是否删除成功\n"}, {"name": "removeAll", "paramTypes": ["java.util.Collection"], "doc": " 批量删除元素\n @param elements 要删除的元素集合\n"}, {"name": "getEstimatedSize", "paramTypes": [], "doc": " 获取当前过滤器中的元素数量估计值\n"}, {"name": "clear", "paramTypes": [], "doc": " 清空过滤器\n"}, {"name": "getStatus", "paramTypes": [], "doc": " 获取过滤器当前状态\n"}, {"name": "reloadFromDatabase", "paramTypes": ["java.util.Collection"], "doc": " 从数据库重新加载数据到过滤器\n"}, {"name": "exportExistingData", "paramTypes": [], "doc": " 导出过滤器当前数据(注意:布谷鸟过滤器不能直接导出所有元素)\n 通常需要结合数据库使用\n"}, {"name": "resize", "paramTypes": ["int"], "doc": " 调整过滤器容量\n"}], "constructors": []}