{"doc": " 评论的点赞与踩Service业务层处理\n\n <AUTHOR>\n @date 2025-06-16\n", "fields": [], "enumConstants": [], "methods": [{"name": "queryPageList", "paramTypes": ["org.ruoyi.system.domain.bo.CommentGoodbadBo", "org.ruoyi.core.page.PageQuery"], "doc": " 查询评论的点赞与踩列表\n"}, {"name": "queryList", "paramTypes": ["org.ruoyi.system.domain.bo.CommentGoodbadBo"], "doc": " 查询评论的点赞与踩列表(非分页)\n"}, {"name": "insertByBo", "paramTypes": ["org.ruoyi.system.domain.bo.CommentGoodbadBo"], "doc": " 新增评论的点赞与踩\n"}, {"name": "updateByBo", "paramTypes": ["org.ruoyi.system.domain.bo.CommentGoodbadBo"], "doc": " 修改评论的点赞与踩\n"}, {"name": "validEntityBeforeSave", "paramTypes": ["org.ruoyi.system.domain.CommentGoodbad"], "doc": " 保存前的数据校验\n"}, {"name": "deleteWithValidByIds", "paramTypes": ["java.util.Collection", "java.lang.Bo<PERSON>an"], "doc": " 批量删除评论的点赞与踩\n"}, {"name": "loadallCache", "paramTypes": ["org.ruoyi.system.domain.bo.CommentGoodbadBo"], "doc": " 非表单查询\n @param bo\n @return\n"}], "constructors": []}