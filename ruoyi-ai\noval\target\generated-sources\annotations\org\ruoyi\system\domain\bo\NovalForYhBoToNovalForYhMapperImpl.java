package org.ruoyi.system.domain.bo;

import java.util.LinkedHashMap;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.ruoyi.system.domain.NovalForYh;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-12T10:45:13+0800",
    comments = "version: 1.5.3.Final, compiler: javac, environment: Java 17.0.12 (Oracle Corporation)"
)
@Component
public class NovalForYhBoToNovalForYhMapperImpl implements NovalForYhBoToNovalForYhMapper {

    @Override
    public NovalForYh convert(NovalForYhBo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        NovalForYh novalForYh = new NovalForYh();

        novalForYh.setSearchValue( arg0.getSearchValue() );
        novalForYh.setCreateDept( arg0.getCreateDept() );
        novalForYh.setCreateBy( arg0.getCreateBy() );
        novalForYh.setCreateTime( arg0.getCreateTime() );
        novalForYh.setUpdateBy( arg0.getUpdateBy() );
        novalForYh.setUpdateTime( arg0.getUpdateTime() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            novalForYh.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        novalForYh.setId( arg0.getId() );
        novalForYh.setName( arg0.getName() );
        novalForYh.setAuthor( arg0.getAuthor() );
        novalForYh.setPlatform( arg0.getPlatform() );
        novalForYh.setGrades( arg0.getGrades() );
        novalForYh.setType( arg0.getType() );
        novalForYh.setLook( arg0.getLook() );

        return novalForYh;
    }

    @Override
    public NovalForYh convert(NovalForYhBo arg0, NovalForYh arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setSearchValue( arg0.getSearchValue() );
        arg1.setCreateDept( arg0.getCreateDept() );
        arg1.setCreateBy( arg0.getCreateBy() );
        arg1.setCreateTime( arg0.getCreateTime() );
        arg1.setUpdateBy( arg0.getUpdateBy() );
        arg1.setUpdateTime( arg0.getUpdateTime() );
        if ( arg1.getParams() != null ) {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.getParams().clear();
                arg1.getParams().putAll( map );
            }
            else {
                arg1.setParams( null );
            }
        }
        else {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.setParams( new LinkedHashMap<String, Object>( map ) );
            }
        }
        arg1.setId( arg0.getId() );
        arg1.setName( arg0.getName() );
        arg1.setAuthor( arg0.getAuthor() );
        arg1.setPlatform( arg0.getPlatform() );
        arg1.setGrades( arg0.getGrades() );
        arg1.setType( arg0.getType() );
        arg1.setLook( arg0.getLook() );

        return arg1;
    }
}
