package org.ruoyi.system.domain.vo;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import javax.annotation.processing.Generated;
import org.ruoyi.system.domain.News;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-12T10:45:13+0800",
    comments = "version: 1.5.3.Final, compiler: javac, environment: Java 17.0.12 (Oracle Corporation)"
)
@Component
public class NewsVoToNewsMapperImpl implements NewsVoToNewsMapper {

    @Override
    public News convert(NewsVo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        News news = new News();

        try {
            if ( arg0.getCreateTime() != null ) {
                news.setCreateTime( new SimpleDateFormat().parse( arg0.getCreateTime() ) );
            }
        }
        catch ( ParseException e ) {
            throw new RuntimeException( e );
        }
        news.setId( arg0.getId() );
        news.setOtherId( arg0.getOtherId() );
        news.setMyId( arg0.getMyId() );
        news.setNews( arg0.getNews() );
        news.setType( arg0.getType() );
        news.setIsread( arg0.getIsread() );

        return news;
    }

    @Override
    public News convert(NewsVo arg0, News arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        try {
            if ( arg0.getCreateTime() != null ) {
                arg1.setCreateTime( new SimpleDateFormat().parse( arg0.getCreateTime() ) );
            }
            else {
                arg1.setCreateTime( null );
            }
        }
        catch ( ParseException e ) {
            throw new RuntimeException( e );
        }
        arg1.setId( arg0.getId() );
        arg1.setOtherId( arg0.getOtherId() );
        arg1.setMyId( arg0.getMyId() );
        arg1.setNews( arg0.getNews() );
        arg1.setType( arg0.getType() );
        arg1.setIsread( arg0.getIsread() );

        return arg1;
    }
}
