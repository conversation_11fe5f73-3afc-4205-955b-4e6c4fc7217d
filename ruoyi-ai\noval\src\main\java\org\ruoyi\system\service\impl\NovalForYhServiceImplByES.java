package org.ruoyi.system.service.impl;

import co.elastic.clients.elasticsearch.ElasticsearchClient;
import co.elastic.clients.elasticsearch._types.Result;
import co.elastic.clients.elasticsearch._types.mapping.LongNumberProperty;
import co.elastic.clients.elasticsearch._types.mapping.Property;
import co.elastic.clients.elasticsearch._types.mapping.TextProperty;
import co.elastic.clients.elasticsearch._types.query_dsl.BoolQuery;
import co.elastic.clients.elasticsearch._types.query_dsl.Query;
import co.elastic.clients.elasticsearch.core.SearchResponse;
import lombok.RequiredArgsConstructor;
import org.ruoyi.common.core.domain.R;
import org.ruoyi.common.core.utils.StringUtils;
import org.ruoyi.core.page.PageQuery;
import org.ruoyi.core.page.TableDataInfo;
import org.ruoyi.system.domain.bo.NovalBo;
import org.ruoyi.system.domain.bo.NovalDetailsBo;
import org.ruoyi.system.domain.bo.NovalForYhBo;
import org.ruoyi.system.domain.bo.SysDictDataBo;
import org.ruoyi.system.domain.vo.NovalDetailsVo;
import org.ruoyi.system.domain.vo.NovalForYhVo;
import org.ruoyi.system.domain.vo.NovalVo;
import org.ruoyi.system.domain.vo.SysDictDataVo;
import org.ruoyi.system.service.INovalDetailsService;
import org.ruoyi.system.service.INovalForYhServiceByES;
import org.ruoyi.system.service.INovalService;
import org.ruoyi.system.util.*;
import org.ruoyi.system.util.Cuckoo.CuckooFilterDict;
import org.ruoyi.system.util.Cuckoo.CuckooFilterUtil;
import org.ruoyi.system.util.Es.ElasticsearchIndexUtil;
import org.ruoyi.system.util.Es.EsFenCi;
import org.ruoyi.system.util.Es.EsIndex;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 用户浏览书库Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-11
 */
@RequiredArgsConstructor
@Service
public class NovalForYhServiceImplByES implements INovalForYhServiceByES, InitializingBean {

    private final INovalService novalService;
    private final INovalDetailsService novalDetailsService;
    private final CuckooFilterUtil<String> cuckooFilterUtilForId;
    private final CuckooFilterUtil<String> cuckooFilterUtilForPlatform;
    private final CuckooFilterUtil<String> cuckooFilterUtilForType;
    private final SysDictDataServiceImpl dictData;

    /**
     * Spring Bean初始化完成后自动执行
     */
    @Override
    public void afterPropertiesSet() throws Exception {
        // 应用启动时自动初始化布谷鸟过滤器
        try {
            createCuckooFilter();
            System.out.println("NovalForYhServiceImplByES: 布谷鸟过滤器自动初始化完成");
        } catch (Exception e) {
            System.err.println("NovalForYhServiceImplByES: 布谷鸟过滤器自动初始化失败: " + e.getMessage());
            // 即使初始化失败，也不影响应用启动
        }
    }
    /**
     * 创建索引（优化单节点ES配置）
     */
    private void creteIndex(ElasticsearchClient client) throws IOException {
        System.out.println("开始创建ES索引: " + EsIndex.NovalForYh);

        // 创建索引映射
        Map<String, Property> properties = new HashMap<>();
        properties.put("id", Property.of(p -> p.long_(LongNumberProperty.of(l -> l))));
        properties.put("author", Property.of(p -> p.text(TextProperty.of(t -> t.fielddata(true)))));
        properties.put("flag", Property.of(p -> p.keyword(t -> t)));
        properties.put("platform", Property.of(p -> p.text(t -> t)));
        properties.put("grades", Property.of(p -> p.text(t -> t)));
        properties.put("type",Property.of(p -> p.text(t -> t)));
        properties.put("summary",Property.of(p -> p.text(t -> t)));
        properties.put("icon",Property.of(p -> p.text(t -> t)));
        properties.put("look",Property.of(p -> p.text(t -> t)));

        // 添加多字段自动补全配置
        // 1. 书名自动补全（支持中文和拼音）
        properties.put("name_suggest", Property.of(p -> p
                .completion(c -> c
                        .analyzer(EsFenCi.ik_max_word)
                        .searchAnalyzer(EsFenCi.ik_smart)
                        .preserveSeparators(false)
                        .preservePositionIncrements(true)
                        .maxInputLength(50)
                )
        ));

        // 2. 作者自动补全
        properties.put("author_suggest", Property.of(p -> p
                .completion(c -> c
                        .analyzer(EsFenCi.ik_max_word)
                        .searchAnalyzer(EsFenCi.ik_smart)
                        .preserveSeparators(false)
                        .preservePositionIncrements(true)
                        .maxInputLength(30)
                )
        ));

        // 3. 类型自动补全
        properties.put("type_suggest", Property.of(p -> p
                .completion(c -> c
                        .analyzer("keyword")
                        .preserveSeparators(false)
                        .preservePositionIncrements(true)
                        .maxInputLength(20)
                )
        ));

        // 4. 综合自动补全字段（包含书名、作者、类型的组合）
        properties.put("all_suggest", Property.of(p -> p
                .completion(c -> c
                        .analyzer(EsFenCi.ik_max_word)
                        .searchAnalyzer(EsFenCi.ik_smart)
                        .preserveSeparators(false)
                        .preservePositionIncrements(true)
                        .maxInputLength(100)
                )
        ));

        // 创建新索引（单节点优化配置）
        boolean result = ElasticsearchIndexUtil.createIndex(client, EsIndex.NovalForYh, properties);
        if (result){
            System.out.println("创建索引成功: " + EsIndex.NovalForYh + " (单节点优化配置)");
        } else {
            System.err.println("创建索引失败: " + EsIndex.NovalForYh);
            throw new IOException("索引创建失败");
        }
    }

    private void createCuckooFilter() {
        // 先添加常用的默认值，确保布谷鸟过滤器始终包含基础参数
        addDefaultFilterValues();

        // 然后从数据库加载完整的字典数据
        try {
            SysDictDataBo sysDictDataBo = new SysDictDataBo();
            sysDictDataBo.setDictType(CuckooFilterDict.NovalType);
            List<SysDictDataVo> sysDictDataVos = dictData.selectDictDataList(sysDictDataBo);
            sysDictDataVos.forEach(sysDictDataVo -> {
                cuckooFilterUtilForType.add(sysDictDataVo.getDictValue());
            });

            sysDictDataBo.setDictType(CuckooFilterDict.NovalPlatform);
            sysDictDataVos = dictData.selectDictDataList(sysDictDataBo);
            sysDictDataVos.forEach(sysDictDataVo -> {
                cuckooFilterUtilForPlatform.add(sysDictDataVo.getDictValue());
            });

            // 初始化所有有效书籍ID到布谷鸟过滤器
            initializeBookIds();
        } catch (Exception e) {
            System.err.println("从数据库加载字典数据失败，使用默认值: " + e.getMessage());
        }
    }

    /**
     * 初始化所有有效书籍ID到布谷鸟过滤器
     */
    private void initializeBookIds() {
        try {
            // 查询所有书籍（包括flag=0的书籍）
            List<NovalVo> allNovels = novalService.queryList(new NovalBo());
            int addedCount = 0;

            for (NovalVo novel : allNovels) {
                // 检查书籍详情是否存在
                NovalDetailsVo details = novalDetailsService.queryById(novel.getId());
                if (details != null) {
                    NovalForYhVo novalForYhVo = new NovalForYhVo(novel, details);
                    // 添加所有有效的书籍（flag >= 0，包括flag="0"的书籍）
                    try {
                        int flagValue = Integer.parseInt(novalForYhVo.getFlag());
                        if (flagValue >= 0) {
                            cuckooFilterUtilForId.add(String.valueOf(novel.getId()));
                            addedCount++;
                        }
                    } catch (NumberFormatException e) {
                        // 如果flag不是数字，默认添加到过滤器
                        cuckooFilterUtilForId.add(String.valueOf(novel.getId()));
                        addedCount++;
                    }
                }
            }

            System.out.println("布谷鸟过滤器ID初始化完成，添加了 " + addedCount + " 个有效书籍ID（包括flag>=0的所有书籍）");
        } catch (Exception e) {
            System.err.println("初始化书籍ID到布谷鸟过滤器失败: " + e.getMessage());
            // 即使初始化失败，也不影响系统运行
        }
    }

    /**
     * 添加默认的过滤器值，确保常用参数始终可用
     */
    private void addDefaultFilterValues() {
        // 添加常用的platform默认值
        String[] defaultPlatforms = {"a", "b", "c", "d", "e", "f", "g", "h", "i", "j"};
        for (String platform : defaultPlatforms) {
            cuckooFilterUtilForPlatform.add(platform);
        }

        // 添加常用的type默认值
        String[] defaultTypes = {"0", "1", "2", "3", "4", "5", "6", "7", "8", "9", "10", "11", "12", "13"};
        for (String type : defaultTypes) {
            cuckooFilterUtilForType.add(type);
        }

        System.out.println("布谷鸟过滤器默认值初始化完成 - Platform: " + defaultPlatforms.length +
                          " 个, Type: " + defaultTypes.length + " 个");
    }

    public void init(ElasticsearchClient client) {
        try {
            createCuckooFilter();
            System.out.println("布谷鸟过滤器初始化完成");
        } catch (Exception e) {
            System.err.println("布谷鸟过滤器初始化失败: " + e.getMessage());
            // 即使初始化失败，也不影响系统运行
        }
    }
    /**
     * 确保ES索引存在，处理索引检查和创建的异常
     * @param client ES客户端
     * @param bo 查询条件
     * @param pageQuery 分页参数
     * @return 索引是否准备就绪
     */
    private boolean ensureIndexExists(ElasticsearchClient client, NovalForYhBo bo, PageQuery pageQuery) {
        try {
            // 检查索引是否存在
            boolean indexExists = false;
            try {
                indexExists = ElasticsearchIndexUtil.indexExists(client, EsIndex.NovalForYh);
                System.out.println("索引存在检查结果: " + indexExists);
            } catch (java.net.ConnectException e) {
                System.err.println("ES连接失败，无法检查索引: " + e.getMessage());
                return false;
            } catch (org.elasticsearch.client.ResponseException e) {
                System.err.println("ES服务响应异常，无法检查索引: " + e.getMessage());
                return false;
            } catch (Exception e) {
                System.err.println("检查索引时发生未知异常: " + e.getMessage());
                // 即使检查失败，也尝试创建索引
                indexExists = false;
            }

            // 如果索引不存在，尝试创建
            if (!indexExists) {
                try {
                    System.out.println("索引不存在，开始创建索引: " + EsIndex.NovalForYh);
                    creteIndex(client);

                    // 创建成功后，加载数据到ES
                    List<NovalForYhVo> novalForYhVoList = loadData(bo, pageQuery);
                    Map<String, NovalForYhVo> map = new HashMap<>();
                    novalForYhVoList.forEach(vo -> map.put(String.valueOf(vo.getId()), vo));
                    ElasticsearchIndexUtil.bulkAddDocuments(client, EsIndex.NovalForYh, map);

                    System.out.println("索引创建并数据加载完成，数据条数: " + novalForYhVoList.size());
                    return true;
                } catch (java.net.ConnectException e) {
                    System.err.println("ES连接失败，无法创建索引: " + e.getMessage());
                    return false;
                } catch (org.elasticsearch.client.ResponseException e) {
                    System.err.println("ES服务响应异常，无法创建索引: " + e.getMessage());
                    return false;
                } catch (Exception e) {
                    System.err.println("创建索引时发生异常: " + e.getMessage());
                    return false;
                }
            } else {
                System.out.println("索引已存在: " + EsIndex.NovalForYh);
                return true;
            }
        } catch (Exception e) {
            System.err.println("索引处理过程中发生未知异常: " + e.getMessage());
            return false;
        }
    }

    /**
     * 查询用户浏览书库列表
     */
    public TableDataInfo<NovalForYhVo> queryPageList(NovalForYhBo bo, PageQuery pageQuery) {
        String platform = bo.getPlatform();
        String type = bo.getType();
        String name = bo.getName();

        ElasticsearchClient client = ElasticsearchIndexUtil.createClient();
        TableDataInfo<NovalForYhVo> novalForYhVoTableDataInfo;
        List<NovalForYhVo> novalForYhVoList;
        Query query;
        Map<String, String> conditions = new HashMap<>();

        // 使用传入的参数而不是硬编码
        if(StringUtils.isNotBlank(platform)) {
            conditions.put("platform", platform);
        }
        if(StringUtils.isNotBlank(type)) {
            conditions.put("type", type);
        }
        if(StringUtils.isNotBlank(name)) {
            conditions.put("name", name);
        }

        try {
            // 独立处理索引检查和创建逻辑，避免异常影响后续查询
            boolean indexReady = ensureIndexExists(client, bo, pageQuery);

            // 如果没有查询条件，返回所有数据
            if(conditions.isEmpty()) {
                novalForYhVoList = loadData(bo, pageQuery);
            } else {
                try {
                    novalForYhVoList = ElasticsearchIndexUtil.searchDocuments(client, EsIndex.NovalForYh, conditions, NovalForYhVo.class);
                    // 如果ES查询结果为空，尝试从数据库加载
                    if(novalForYhVoList.isEmpty()) {
                        System.out.println("ES查询结果为空，尝试从数据库加载数据");
                        novalForYhVoList = loadData(bo, pageQuery);
                    }
                } catch (Exception e) {
                    System.out.println("ES查询失败，从数据库加载数据: " + e.getMessage());
                    novalForYhVoList = loadData(bo, pageQuery);
                }
            }
            novalForYhVoTableDataInfo = TableDataInfo.build(novalForYhVoList);

            // 只在有结果时输出
            if (novalForYhVoList.size() > 0) {
                System.out.println("NovalForYh查询结果 - 总数: " + novalForYhVoTableDataInfo.getTotal() + ", 数据条数: " + novalForYhVoList.size());
            }
        } catch (Exception e) {
            System.err.println("查询过程中发生异常: " + e.getMessage());
            // 其他异常也降级到数据库查询
            System.err.println("发生异常，降级到数据库查询");
            novalForYhVoList = loadData(bo, pageQuery);
            novalForYhVoTableDataInfo = TableDataInfo.build(novalForYhVoList);
        } finally {
            client.shutdown();
        }
        return novalForYhVoTableDataInfo;
    }


    /**
     * 查询用户浏览书库列表
     */
    public List<NovalForYhVo> loadData(NovalForYhBo bo, PageQuery pageQuery) {
        List<NovalForYhVo> novalForYhVoList = new ArrayList<>();
        String platform = bo.getPlatform();
        String type = bo.getType();
        NovalDetailsBo novalDetailsBo = new NovalDetailsBo();
        novalDetailsBo.setPlatform(platform);
        novalDetailsBo.setType(type);
        TableDataInfo<NovalDetailsVo> novalDetailsVoTableDataInfo = null;
        // 检查布谷鸟过滤器，现在有默认值，通常不需要重新初始化
        if(cuckooFilterUtilForPlatform.contains(platform) && cuckooFilterUtilForType.contains(type)){
            novalDetailsVoTableDataInfo = novalDetailsService.queryPageList(novalDetailsBo, pageQuery);
            novalDetailsVoTableDataInfo.getRows().forEach(novalDetailsVo -> {
                NovalVo novalVo = novalService.queryById(novalDetailsVo.getId());
                NovalForYhVo novalForYhVo = new NovalForYhVo(novalVo, novalDetailsVo);
                if(!novalForYhVo.getFlag().equals("0")) {
                    cuckooFilterUtilForId.add(String.valueOf(novalForYhVo.getId()));
                    novalForYhVoList.add(novalForYhVo);
                }
            });
            SortUtils.sortList(novalForYhVoList, NovalForYhVo::getLook, SortUtils.ASCENDING);
        }
        return novalForYhVoList;
    }


    public NovalForYhVo queryById(Long id) {
        System.out.println("queryById开始查询，ID: " + id);
        ElasticsearchClient client = ElasticsearchIndexUtil.createClient();
        NovalForYhVo novalForYhVo = null;

        try {
            // 尝试从ES获取数据
            novalForYhVo = ElasticsearchIndexUtil.getDocument(client, EsIndex.NovalForYh, String.valueOf(id), NovalForYhVo.class);

            if(novalForYhVo == null){
                System.out.println("ES中未找到数据，尝试从数据库加载，ID: " + id);
                // ES中没有数据，尝试从数据库加载并更新到ES
                try {
                    novalForYhVo = updateEs(client, id);
                    System.out.println("从数据库加载并更新ES成功，ID: " + id);
                } catch (Exception esUpdateException) {
                    // ES更新失败（可能是磁盘空间不足），直接从数据库获取
                    System.err.println("ES更新失败，直接从数据库获取数据: " + esUpdateException.getMessage());
                    novalForYhVo = loadFromDatabase(id);
                }
            } else {
                System.out.println("从ES获取数据成功，ID: " + id);
            }
        } catch (Exception e) {
            // ES查询失败，降级到数据库查询
            System.err.println("ES查询失败，降级到数据库查询: " + e.getMessage());
            novalForYhVo = loadFromDatabase(id);
        } finally {
            client.shutdown();
        }

        if (novalForYhVo != null) {
            System.out.println("queryById查询成功，返回数据: " + novalForYhVo.getName());
        } else {
            System.err.println("queryById查询失败，未找到数据，ID: " + id);
        }

        return novalForYhVo;
    }

    /**
     * 从数据库直接加载数据
     */
    private NovalForYhVo loadFromDatabase(Long id) {
        try {
            NovalVo novalVo = novalService.queryById(id);
            NovalDetailsVo novalDetailsVo = novalDetailsService.queryById(id);

            if (novalVo == null || novalDetailsVo == null) {
                System.err.println("数据库中未找到ID为 " + id + " 的小说数据");
                return null;
            }

            return new NovalForYhVo(novalVo, novalDetailsVo);
        } catch (Exception e) {
            System.err.println("从数据库加载数据失败: " + e.getMessage());
            return null;
        }
    }

    public boolean addEs(NovalForYhVo novalForYhVo) {
        ElasticsearchClient client = ElasticsearchIndexUtil.createClient();
        try {
            cuckooFilterUtilForId.add(String.valueOf(novalForYhVo.getId()));
            ElasticsearchIndexUtil.addDocument(client, EsIndex.NovalForYh,novalForYhVo);
        } catch (Exception e) {
            System.err.println("addEs方法添加数据到ES失败: " + e.getMessage());
            // 检查是否是磁盘空间不足的错误
            if (e.getMessage().contains("TOO_MANY_REQUESTS") || e.getMessage().contains("disk usage exceeded")) {
                System.err.println("ES磁盘空间不足，跳过ES操作");
            }
            return false;
        } finally {
            client.shutdown();
        }
        return true;
    }
    public NovalForYhVo updateEs(ElasticsearchClient client, Long id) throws IOException {
        NovalVo novalVo = novalService.queryById(id);
        NovalDetailsVo novalDetailsVo = novalDetailsService.queryById(id);
        if (novalVo == null || novalDetailsVo == null) {
            throw new IllegalArgumentException("未找到ID为 " + id + " 的小说基础数据");
        }
        NovalForYhVo novalForYhVo = new NovalForYhVo(novalVo, novalDetailsVo);

        try {
            ElasticsearchIndexUtil.updateDocument(client, EsIndex.NovalForYh, String.valueOf(id), novalForYhVo);
        } catch (Exception e) {
            System.err.println("ES更新文档失败: " + e.getMessage());
            // 检查是否是磁盘空间不足的错误
            if (e.getMessage().contains("TOO_MANY_REQUESTS") || e.getMessage().contains("disk usage exceeded")) {
                System.err.println("ES磁盘空间不足，跳过ES更新操作，直接返回数据");
            } else {
                // 其他类型的错误，重新抛出
                throw e;
            }
        }

        return novalForYhVo;
    }

    public R<Map<String,Object>> updateEsByMap(Map<String,Object> map,Long id) {
        ElasticsearchClient client = ElasticsearchIndexUtil.createClient();
        cuckooFilterUtilForId.add(String.valueOf(id));
        R<Map<String,Object>> result = null;
        try {
            // ElasticsearchIndexUtil.updateDocument返回ES的Result枚举，需要转换为业务R对象
            co.elastic.clients.elasticsearch._types.Result esResult = ElasticsearchIndexUtil.updateDocument(client, EsIndex.NovalForYh, String.valueOf(id), map);

            // 根据ES操作结果构建业务响应
            if (esResult == co.elastic.clients.elasticsearch._types.Result.Updated ||
                esResult == co.elastic.clients.elasticsearch._types.Result.NoOp) {
                result = R.ok("ES更新成功", map);
            } else {
                result = R.fail("ES更新失败，操作结果: " + esResult.toString());
            }
        } catch (Exception e) {
            System.err.println("updateEsByMap方法ES更新失败: " + e.getMessage());
            // 检查是否是磁盘空间不足的错误
            if (e.getMessage().contains("TOO_MANY_REQUESTS") || e.getMessage().contains("disk usage exceeded")) {
                // 返回一个表示成功的结果，避免影响业务流程
                result = R.ok("磁盘空间不足，ES更新失败，但已跳过该操作", map);
            } else {
                result = R.fail("ES更新异常: " + e.getMessage());
            }
        } finally {
            client.shutdown();
        }

        return result;
    }

    public void deleteEs(ElasticsearchClient client, Long id) {
        try {
            cuckooFilterUtilForId.remove(String.valueOf(id));
            ElasticsearchIndexUtil.deleteDocument(client, EsIndex.NovalForYh, String.valueOf(id));
        } catch (Exception e) {
            System.err.println("ES删除文档失败: " + e.getMessage());
            // 检查是否是磁盘空间不足的错误
            if (e.getMessage().contains("TOO_MANY_REQUESTS") || e.getMessage().contains("disk usage exceeded")) {
                System.err.println("ES磁盘空间不足，跳过ES删除操作");
            } else {
                throw new RuntimeException(e);
            }
        }
    }

    @Override
    public List<NovalForYhVo> queryByUserId(Long[] ids) {
        if(ids == null || ids.length == 0){
            return null;
        }
        List<NovalForYhVo> novalForYhVoList = new ArrayList<>();
            for(Long id : ids){
                boolean contains = cuckooFilterUtilForId.contains(String.valueOf(id));
                System.out.println("--------------------");
                System.out.println("cuckooFilterUtilForId:"+contains);
                System.out.println("--------------------");
                if(contains) {
                    novalForYhVoList.add(queryById(id));
                }
            }
        return novalForYhVoList;
    }

    @Override
    public List<String> autoSuggest(String keyword) {
        if (keyword == null || keyword.trim().isEmpty()) {
            return new ArrayList<>();
        }

        ElasticsearchClient client = ElasticsearchIndexUtil.createClient();
        try {
            // 使用多字段自动补全，支持书名、作者、类型和综合搜索
            String[] suggestFields = {
                "name_suggest",      // 书名自动补全
                "author_suggest",    // 作者自动补全
                "type_suggest",      // 类型自动补全
                "all_suggest"        // 综合自动补全
            };

            List<String> suggestions = ElasticsearchIndexUtil.multiFieldAutoSuggest(
                    client,
                    EsIndex.NovalForYh,
                    keyword.trim(),
                    suggestFields,
                    15  // 最多返回15个建议
            );

            // 进一步过滤和优化结果
            return optimizeSuggestions(suggestions, keyword);

        } catch (Exception e) {
            System.err.println("ES多字段自动补全查询失败: " + e.getMessage());

            // 降级到单字段查询
            try {
                return ElasticsearchIndexUtil.autoSuggest(
                        client,
                        EsIndex.NovalForYh,
                        keyword.trim(),
                        "name_suggest"
                );
            } catch (Exception fallbackException) {
                System.err.println("ES单字段自动补全也失败: " + fallbackException.getMessage());

                // 检查是否是磁盘空间不足的错误
                if (e.getMessage().contains("TOO_MANY_REQUESTS") || e.getMessage().contains("disk usage exceeded")) {
                    System.err.println("ES磁盘空间不足，返回空的自动补全结果");
                    return new ArrayList<>();
                } else {
                    // 最后降级：返回数据库中的简单匹配
                    return getDatabaseSuggestions(keyword);
                }
            }
        } finally {
            client.shutdown();
        }
    }

    /**
     * 优化自动补全建议结果
     * @param suggestions 原始建议列表
     * @param keyword 用户输入的关键词
     * @return 优化后的建议列表
     */
    private List<String> optimizeSuggestions(List<String> suggestions, String keyword) {
        if (suggestions == null || suggestions.isEmpty()) {
            return new ArrayList<>();
        }

        String lowerKeyword = keyword.toLowerCase().trim();

        return suggestions.stream()
                .filter(suggestion -> suggestion != null && !suggestion.trim().isEmpty())
                .distinct()  // 去重
                .sorted((s1, s2) -> {
                    // 智能排序：优先显示以关键词开头的建议
                    String lower1 = s1.toLowerCase();
                    String lower2 = s2.toLowerCase();

                    boolean starts1 = lower1.startsWith(lowerKeyword);
                    boolean starts2 = lower2.startsWith(lowerKeyword);

                    if (starts1 && !starts2) return -1;
                    if (!starts1 && starts2) return 1;

                    // 其次按包含关键词的位置排序
                    int pos1 = lower1.indexOf(lowerKeyword);
                    int pos2 = lower2.indexOf(lowerKeyword);

                    if (pos1 != pos2) {
                        if (pos1 == -1) return 1;
                        if (pos2 == -1) return -1;
                        return Integer.compare(pos1, pos2);
                    }

                    // 最后按长度排序（短的优先）
                    return Integer.compare(s1.length(), s2.length());
                })
                .limit(10)  // 最多返回10个建议
                .collect(Collectors.toList());
    }

    /**
     * 数据库降级查询自动补全建议
     * @param keyword 用户输入的关键词
     * @return 建议列表
     */
    private List<String> getDatabaseSuggestions(String keyword) {
        try {
            // 从数据库中查询匹配的书名和作者
            List<String> suggestions = new ArrayList<>();

            // 查询书名匹配的记录
            NovalForYhBo nameQuery = new NovalForYhBo();
            nameQuery.setName(keyword);
            List<NovalForYhVo> nameResults = loadData(nameQuery, new PageQuery(5, 1));

            nameResults.stream()
                    .map(NovalForYhVo::getName)
                    .filter(name -> name != null && name.toLowerCase().contains(keyword.toLowerCase()))
                    .forEach(suggestions::add);

            // 查询作者匹配的记录
            NovalForYhBo authorQuery = new NovalForYhBo();
            authorQuery.setAuthor(keyword);
            List<NovalForYhVo> authorResults = loadData(authorQuery, new PageQuery(5, 1));

            authorResults.stream()
                    .map(NovalForYhVo::getAuthor)
                    .filter(author -> author != null && author.toLowerCase().contains(keyword.toLowerCase()))
                    .forEach(suggestions::add);

            return suggestions.stream()
                    .distinct()
                    .limit(8)
                    .collect(Collectors.toList());

        } catch (Exception e) {
            System.err.println("数据库降级查询自动补全失败: " + e.getMessage());
            return new ArrayList<>();
        }
    }
}
