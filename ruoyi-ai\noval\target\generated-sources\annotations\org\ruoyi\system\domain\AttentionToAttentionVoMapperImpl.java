package org.ruoyi.system.domain;

import javax.annotation.processing.Generated;
import org.ruoyi.system.domain.vo.AttentionVo;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-12T10:45:13+0800",
    comments = "version: 1.5.3.Final, compiler: javac, environment: Java 17.0.12 (Oracle Corporation)"
)
@Component
public class AttentionToAttentionVoMapperImpl implements AttentionToAttentionVoMapper {

    @Override
    public AttentionVo convert(Attention arg0) {
        if ( arg0 == null ) {
            return null;
        }

        AttentionVo attentionVo = new AttentionVo();

        attentionVo.setId( arg0.getId() );
        attentionVo.setOtherId( arg0.getOtherId() );
        attentionVo.setCreateTime( arg0.getCreateTime() );

        return attentionVo;
    }

    @Override
    public AttentionVo convert(Attention arg0, AttentionVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setOtherId( arg0.getOtherId() );
        arg1.setCreateTime( arg0.getCreateTime() );

        return arg1;
    }
}
