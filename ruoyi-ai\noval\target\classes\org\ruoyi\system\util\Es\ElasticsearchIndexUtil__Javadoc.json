{"doc": "", "fields": [], "enumConstants": [], "methods": [{"name": "createClient", "paramTypes": [], "doc": " 创建/获取Elasticsearch客户端 (单例模式)\n"}, {"name": "isClientActive", "paramTypes": ["co.elastic.clients.elasticsearch.ElasticsearchClient"], "doc": " 检查客户端是否活跃\n"}, {"name": "closeCurrentClient", "paramTypes": [], "doc": " 关闭当前客户端\n"}, {"name": "createIndex", "paramTypes": ["co.elastic.clients.elasticsearch.ElasticsearchClient", "java.lang.String", "java.util.Map"], "doc": " 创建索引（优化单节点配置）\n"}, {"name": "indexExists", "paramTypes": ["co.elastic.clients.elasticsearch.ElasticsearchClient", "java.lang.String"], "doc": " 检查索引是否存在\n"}, {"name": "docExists", "paramTypes": ["co.elastic.clients.elasticsearch.ElasticsearchClient", "java.lang.String", "java.lang.String"], "doc": " 检查文档是否存在\n"}, {"name": "deleteIndex", "paramTypes": ["co.elastic.clients.elasticsearch.ElasticsearchClient", "java.lang.String"], "doc": " 删除索引\n"}, {"name": "addDocument", "paramTypes": ["co.elastic.clients.elasticsearch.ElasticsearchClient", "java.lang.String", "java.lang.Object"], "doc": " 添加文档（自动生成ID）\n\n @param <T> 文档类型\n @return 生成的文档ID\n"}, {"name": "upsertDocument", "paramTypes": ["co.elastic.clients.elasticsearch.ElasticsearchClient", "java.lang.String", "java.lang.String", "java.lang.Object"], "doc": " 添加或更新文档（指定ID）\n\n @param <T> 文档类型\n @return 操作结果（CREATED 或 UPDATED）\n"}, {"name": "updateDocument", "paramTypes": ["co.elastic.clients.elasticsearch.ElasticsearchClient", "java.lang.String", "java.lang.String", "java.lang.Object"], "doc": " 更新文档（部分字段）\n\n @return 操作结果（UPDATED 或 NOOP）\n"}, {"name": "deleteDocument", "paramTypes": ["co.elastic.clients.elasticsearch.ElasticsearchClient", "java.lang.String", "java.lang.String"], "doc": " 删除文档\n\n @return 操作结果（DELETED 或 NOT_FOUND）\n"}, {"name": "getDocument", "paramTypes": ["co.elastic.clients.elasticsearch.ElasticsearchClient", "java.lang.String", "java.lang.String", "java.lang.Class"], "doc": " 获取文档\n\n @param <T> 文档类型\n @return 文档对象（未找到返回null）\n"}, {"name": "bulkAddDocuments", "paramTypes": ["co.elastic.clients.elasticsearch.ElasticsearchClient", "java.lang.String", "java.util.Map"], "doc": " 批量添加文档\n\n @param <T> 文档类型\n @return 批量操作结果\n"}, {"name": "searchDocuments", "paramTypes": ["co.elastic.clients.elasticsearch.ElasticsearchClient", "java.lang.String", "java.lang.String", "java.lang.String", "java.lang.Class"], "doc": " 搜索文档\n\n @param <T> 文档类型\n @return 匹配的文档列表\n"}, {"name": "closeClient", "paramTypes": ["co.elastic.clients.elasticsearch.ElasticsearchClient"], "doc": " 关闭客户端连接\n"}, {"name": "analyze", "paramTypes": ["co.elastic.clients.elasticsearch.ElasticsearchClient", "java.lang.String", "java.lang.String", "java.lang.String"], "doc": " 文本分词\n\n @param client Elasticsearch客户端\n @param index 索引名称（可为空）\n @param text 待分词文本\n @param analyzer 分词器名称\n @return 分词结果列表\n @throws IOException\n"}, {"name": "autoSuggest", "paramTypes": ["co.elastic.clients.elasticsearch.ElasticsearchClient", "java.lang.String", "java.lang.String", "java.lang.String"], "doc": " 自动补全建议\n\n @param client Elasticsearch客户端\n @param index 索引名称\n @param keyword 用户输入的关键词\n @param fieldName 补全字段名称（默认为\"tags\"）\n @return 补全建议列表\n @throws IOException\n"}, {"name": "multiFieldAutoSuggest", "paramTypes": ["co.elastic.clients.elasticsearch.ElasticsearchClient", "java.lang.String", "java.lang.String", "java.lang.String[]", "int"], "doc": " 多字段自动补全建议（增强版）\n\n @param client Elasticsearch客户端\n @param index 索引名称\n @param keyword 用户输入的关键词\n @param fieldNames 多个补全字段名称\n @param maxSize 最大返回数量\n @return 补全建议列表（去重排序）\n @throws IOException\n"}, {"name": "searchDocuments", "paramTypes": ["co.elastic.clients.elasticsearch.ElasticsearchClient", "java.lang.String", "java.util.Map", "java.lang.Class"], "doc": " 多条件AND搜索文档\n\n @param <T> 文档类型\n @param conditions 查询条件Map(key=字段名, value=字段值)\n @return 匹配的文档列表\n"}, {"name": "searchDocumentsOr", "paramTypes": ["co.elastic.clients.elasticsearch.ElasticsearchClient", "java.lang.String", "java.util.Map", "java.lang.Class"], "doc": " 多条件OR搜索文档\n\n @param <T> 文档类型\n @param conditions 查询条件Map(key=字段名, value=字段值)\n @return 匹配的文档列表\n"}], "constructors": []}