package org.ruoyi.system.domain.vo;

import javax.annotation.processing.Generated;
import org.ruoyi.system.domain.Noval;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-12T10:45:12+0800",
    comments = "version: 1.5.3.Final, compiler: javac, environment: Java 17.0.12 (Oracle Corporation)"
)
@Component
public class NovalVoToNovalMapperImpl implements NovalVoToNovalMapper {

    @Override
    public Noval convert(NovalVo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        Noval noval = new Noval();

        noval.setCreateTime( arg0.getCreateTime() );
        noval.setId( arg0.getId() );
        noval.setName( arg0.getName() );
        noval.setFlag( arg0.getFlag() );
        noval.setAuthor( arg0.getAuthor() );

        return noval;
    }

    @Override
    public Noval convert(NovalVo arg0, Noval arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setCreateTime( arg0.getCreateTime() );
        arg1.setId( arg0.getId() );
        arg1.setName( arg0.getName() );
        arg1.setFlag( arg0.getFlag() );
        arg1.setAuthor( arg0.getAuthor() );

        return arg1;
    }
}
