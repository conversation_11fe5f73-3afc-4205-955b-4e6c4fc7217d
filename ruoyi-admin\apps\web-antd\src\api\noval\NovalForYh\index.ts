import type { NovalForYhVO, NovalForYhForm, NovalForYhQuery } from './model';

import type { ID, IDS } from '#/api/common';
import type { PageResult } from '#/api/common';

import { commonExport } from '#/api/helper';
import { requestClient } from '#/api/request';

/**
* 查询用户浏览书库列表
* @param params
* @returns 用户浏览书库列表
*/
export function NovalForYhList(params?: NovalForYhQuery) {
  // 如果有ID参数，使用getInfo接口，否则使用list接口
  if (params?.id) {
    return requestClient.get<PageResult<NovalForYhVO>>(`/noval/NovalForYh/getInfo/${params.id}`, {
      timeout: 60000 // 60秒超时，适应ES降级到数据库查询的情况
    });
  }
  return requestClient.get<PageResult<NovalForYhVO>>('/noval/NovalForYh/list', {
    params,
    timeout: 60000 // 60秒超时，适应ES降级到数据库查询的情况
  });
}

/**
 * 导出用户浏览书库列表
 * @param params
 * @returns 用户浏览书库列表
 */
export function NovalForYhExport(params?: NovalForYhQuery) {
  return commonExport('/noval/NovalForYh/export', params ?? {});
}

/**
 * 查询用户浏览书库详情
 * @param id id
 * @returns 用户浏览书库详情
 */
export function NovalForYhInfo(id: ID) {
  return requestClient.get<NovalForYhVO>(`/noval/NovalForYh/${id}`, {
    timeout: 60000 // 60秒超时，适应ES降级到数据库查询的情况
  });
}

/**
 * 新增用户浏览书库
 * @param data
 * @returns void
 */
export function NovalForYhAdd(data: NovalForYhForm) {
  return requestClient.postWithMsg<void>('/noval/NovalForYh', data);
}

/**
 * 更新用户浏览书库
 * @param data
 * @returns void
 */
export function NovalForYhUpdate(data: NovalForYhForm) {
  return requestClient.putWithMsg<void>('/noval/NovalForYh', data);
}

/**
 * 删除用户浏览书库
 * @param id id
 * @returns void
 */
export function NovalForYhRemove(id: ID | IDS) {
  return requestClient.deleteWithMsg<void>(`/noval/NovalForYh/${id}`);
}

export function NovalForYhGetInfoByIds(ids: number[]) {
  if (ids.length === 0) {
    return Promise.resolve([]);
  }
  return requestClient.get<NovalForYhVO[]>(`/noval/NovalForYh/getInfo/${ids.join(',')}`, {
    timeout: 60000 // 60秒超时，适应ES降级到数据库查询的情况
  });
}

/**
 * 搜索框自动补全（增强版）
 * 支持书名、作者、类型等多字段智能补全
 * @param keyword 关键词
 * @returns 自动补全建议列表
 */
export function NovalForYhSuggest(keyword: string) {
  console.log('=== API调用开始 ===');
  console.log('API接收关键词:', keyword);

  if (!keyword || keyword.trim().length < 1) {
    console.log('关键词为空，直接返回空数组');
    return Promise.resolve([]);
  }

  const trimmedKeyword = keyword.trim();
  console.log('处理后的关键词:', trimmedKeyword);
  console.log('请求URL: /noval/NovalForYh/suggest');
  console.log('请求参数:', { keyword: trimmedKeyword });

  return requestClient.get<string[]>('/noval/NovalForYh/suggest', {
    params: { keyword: trimmedKeyword },
    timeout: 5000  // 5秒超时，确保自动补全响应快速
  }).then(response => {
    console.log('=== API响应成功 ===');
    console.log('原始响应:', response);
    console.log('响应类型:', typeof response);

    // 如果返回的是包装格式 {code: 200, data: [...]}，提取data
    if (response && typeof response === 'object' && 'data' in response) {
      const data = (response as any).data || [];
      console.log('提取的data:', data);
      return data;
    }
    // 如果直接返回数组
    console.log('直接返回响应:', response || []);
    return response || [];
  }).catch(error => {
    console.error('=== API请求失败 ===');
    console.error('错误详情:', error);
    console.error('错误类型:', error.constructor.name);
    if (error.response) {
      console.error('响应状态:', error.response.status);
      console.error('响应数据:', error.response.data);
    }
    return [];  // 失败时返回空数组，不影响用户体验
  });
}


