package org.ruoyi.system.domain.bo;

import java.util.LinkedHashMap;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.ruoyi.system.domain.News;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-12T10:45:13+0800",
    comments = "version: 1.5.3.Final, compiler: javac, environment: Java 17.0.12 (Oracle Corporation)"
)
@Component
public class NewsBoToNewsMapperImpl implements NewsBoToNewsMapper {

    @Override
    public News convert(NewsBo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        News news = new News();

        news.setSearchValue( arg0.getSearchValue() );
        news.setCreateDept( arg0.getCreateDept() );
        news.setCreateBy( arg0.getCreateBy() );
        news.setCreateTime( arg0.getCreateTime() );
        news.setUpdateBy( arg0.getUpdateBy() );
        news.setUpdateTime( arg0.getUpdateTime() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            news.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        news.setId( arg0.getId() );
        news.setOtherId( arg0.getOtherId() );
        news.setMyId( arg0.getMyId() );
        news.setNews( arg0.getNews() );
        news.setType( arg0.getType() );
        news.setIsread( arg0.getIsread() );

        return news;
    }

    @Override
    public News convert(NewsBo arg0, News arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setSearchValue( arg0.getSearchValue() );
        arg1.setCreateDept( arg0.getCreateDept() );
        arg1.setCreateBy( arg0.getCreateBy() );
        arg1.setCreateTime( arg0.getCreateTime() );
        arg1.setUpdateBy( arg0.getUpdateBy() );
        arg1.setUpdateTime( arg0.getUpdateTime() );
        if ( arg1.getParams() != null ) {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.getParams().clear();
                arg1.getParams().putAll( map );
            }
            else {
                arg1.setParams( null );
            }
        }
        else {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.setParams( new LinkedHashMap<String, Object>( map ) );
            }
        }
        arg1.setId( arg0.getId() );
        arg1.setOtherId( arg0.getOtherId() );
        arg1.setMyId( arg0.getMyId() );
        arg1.setNews( arg0.getNews() );
        arg1.setType( arg0.getType() );
        arg1.setIsread( arg0.getIsread() );

        return arg1;
    }
}
